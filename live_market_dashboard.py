import os
import json
import time
import threading
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from fyers_apiv3 import fyersModel
from fyers_apiv3.FyersWebsocket import data_ws
# import pandas_ta as ta  # Commented out due to numpy compatibility issues
from collections import defaultdict, deque
import logging

class LiveMarketDashboard:
    """
    Comprehensive Live Market Dashboard with WebSocket streaming,
    technical analysis, pre-market scanning, and signal detection
    """
    
    def __init__(self, fyers_client):
        self.fyers = fyers_client
        self.ws = None
        self.is_connected = False
        
        # Data storage
        self.live_data = {}
        self.historical_data = {}
        self.depth_data = {}
        self.volume_data = defaultdict(deque)
        self.price_data = defaultdict(deque)
        
        # Scanner settings
        self.scanner_symbols = []
        self.monitored_symbols = set()
        self.signals = []
        self.volume_threshold = 1.5  # 1.5x average volume
        self.gap_threshold = 1.5     # 1.5% gap up
        self.rsi_entry_level = 60    # RSI crossing above 60
        
        # Technical indicators cache
        self.indicators = defaultdict(dict)
        
        # Logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def initialize_websocket(self):
        """Initialize WebSocket connection for live data"""
        try:
            if not self.fyers:
                raise Exception("Fyers client not initialized")

            # Get access token from environment or token manager
            access_token = os.environ.get('FYERS_ACCESS_TOKEN')
            if not access_token:
                # Try to get from token manager
                from token_manager import FyersTokenManager
                token_manager = FyersTokenManager()
                access_token = token_manager.get_valid_access_token()

            if not access_token:
                raise Exception("No valid access token available for WebSocket")

            # Create WebSocket instance
            self.ws = data_ws.FyersDataSocket(
                access_token=access_token,
                log_path="",
                litemode=False
            )
            
            # Set up event handlers
            self.ws.websocket_data = self.on_message
            self.ws.on_connect = self.on_connect
            self.ws.on_disconnect = self.on_disconnect
            self.ws.on_error = self.on_error
            
            return True
            
        except Exception as e:
            self.logger.error(f"WebSocket initialization failed: {e}")
            return False
    
    def on_connect(self):
        """WebSocket connection established"""
        self.is_connected = True
        self.logger.info("WebSocket connected successfully")
        
    def on_disconnect(self):
        """WebSocket disconnected"""
        self.is_connected = False
        self.logger.info("WebSocket disconnected")
        
    def on_error(self, error):
        """WebSocket error handler"""
        self.logger.error(f"WebSocket error: {error}")
        
    def on_message(self, message):
        """Process incoming WebSocket data"""
        try:
            if isinstance(message, dict):
                symbol = message.get('symbol', '')
                
                # Handle different message types
                if 'ltp' in message:
                    self.process_ltp_data(symbol, message)
                elif 'depth' in message:
                    self.process_depth_data(symbol, message)
                elif 'volume' in message:
                    self.process_volume_data(symbol, message)
                    
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
    
    def process_ltp_data(self, symbol, data):
        """Process Last Traded Price data"""
        timestamp = datetime.now()
        ltp = data.get('ltp', 0)
        volume = data.get('volume', 0)
        
        # Store live data
        self.live_data[symbol] = {
            'ltp': ltp,
            'volume': volume,
            'timestamp': timestamp,
            'change': data.get('change', 0),
            'change_percent': data.get('change_percent', 0)
        }
        
        # Update price history for technical analysis
        self.price_data[symbol].append({
            'timestamp': timestamp,
            'price': ltp,
            'volume': volume
        })
        
        # Keep only last 1000 data points
        if len(self.price_data[symbol]) > 1000:
            self.price_data[symbol].popleft()
            
        # Check for signals if symbol is monitored
        if symbol in self.monitored_symbols:
            self.check_entry_signals(symbol)
    
    def process_depth_data(self, symbol, data):
        """Process order book depth data"""
        self.depth_data[symbol] = {
            'bids': data.get('bids', []),
            'asks': data.get('asks', []),
            'timestamp': datetime.now()
        }
    
    def process_volume_data(self, symbol, data):
        """Process volume data for threshold verification"""
        volume = data.get('volume', 0)
        timestamp = datetime.now()
        
        self.volume_data[symbol].append({
            'timestamp': timestamp,
            'volume': volume
        })
        
        # Keep only last 100 volume data points
        if len(self.volume_data[symbol]) > 100:
            self.volume_data[symbol].popleft()
    
    def subscribe_symbols(self, symbols):
        """Subscribe to live data for given symbols"""
        try:
            if not self.is_connected:
                if not self.initialize_websocket():
                    return False
                    
            # Subscribe to symbol updates
            symbol_list = [f"NSE:{symbol}-EQ" if not symbol.startswith("NSE:") else symbol for symbol in symbols]
            
            # Subscribe to LTP updates (correct parameter name)
            self.ws.subscribe(symbols=symbol_list, data_type="SymbolUpdate")

            # Subscribe to depth updates for key symbols
            key_symbols = symbol_list[:50]  # Limit depth subscription
            self.ws.subscribe(symbols=key_symbols, data_type="DepthUpdate")
            
            self.monitored_symbols.update(symbols)
            self.logger.info(f"Subscribed to {len(symbols)} symbols")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Subscription failed: {e}")
            return False
    
    def get_historical_data(self, symbol, resolution="5", days=30):
        """Fetch historical OHLCV data for technical analysis"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Format symbol for Fyers API
            fyers_symbol = f"NSE:{symbol}-EQ" if not symbol.startswith("NSE:") else symbol
            
            data = {
                "symbol": fyers_symbol,
                "resolution": resolution,
                "date_format": "1",
                "range_from": start_date.strftime("%Y-%m-%d"),
                "range_to": end_date.strftime("%Y-%m-%d"),
                "cont_flag": "1"
            }
            
            response = self.fyers.history(data=data)
            
            if response['s'] == 'ok':
                candles = response['candles']
                df = pd.DataFrame(candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                df.set_index('timestamp', inplace=True)
                
                self.historical_data[symbol] = df
                return df
            else:
                self.logger.error(f"Historical data fetch failed for {symbol}: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    def calculate_technical_indicators(self, symbol):
        """Calculate technical indicators for a symbol"""
        try:
            if symbol not in self.historical_data:
                self.get_historical_data(symbol)
                
            df = self.historical_data.get(symbol)
            if df is None or len(df) < 50:
                return None
                
            # Calculate indicators using simple pandas operations
            indicators = {}

            # RSI (14)
            indicators['rsi'] = self.calculate_rsi(df['close'], 14)

            # VWAP
            indicators['vwap'] = self.calculate_vwap(df)

            # Moving Averages
            indicators['sma_20'] = df['close'].rolling(window=20).mean()
            indicators['ema_20'] = df['close'].ewm(span=20).mean()

            # Volume indicators
            indicators['volume_sma'] = df['volume'].rolling(window=20).mean()

            # Bollinger Bands
            bb_data = self.calculate_bollinger_bands(df['close'], 20, 2)
            indicators['bb_upper'] = bb_data['upper']
            indicators['bb_middle'] = bb_data['middle']
            indicators['bb_lower'] = bb_data['lower']
            
            # Store indicators
            self.indicators[symbol] = indicators
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            return None

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI using simple pandas operations"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series([np.nan] * len(prices), index=prices.index)

    def calculate_vwap(self, df):
        """Calculate Volume Weighted Average Price"""
        try:
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
            return vwap
        except Exception:
            return pd.Series([np.nan] * len(df), index=df.index)

    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        try:
            middle = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)

            return {
                'upper': upper,
                'middle': middle,
                'lower': lower
            }
        except Exception:
            return {
                'upper': pd.Series([np.nan] * len(prices), index=prices.index),
                'middle': pd.Series([np.nan] * len(prices), index=prices.index),
                'lower': pd.Series([np.nan] * len(prices), index=prices.index)
            }

    def run_premarket_scanner(self):
        """Run scanner to identify gap-up stocks (works anytime)"""
        try:
            scanner_results = []
            current_time = datetime.now().time()

            # Scanner now works anytime (removed time restriction)
            self.logger.info(f"Running scanner at {current_time.strftime('%H:%M:%S')}")

            # Get market snapshots
            symbols_to_scan = self.get_fno_symbols()[:100]  # Scan top 100 F&O stocks

            for symbol in symbols_to_scan:
                try:
                    # Get current price and previous close
                    quote_data = self.get_quote_data(symbol)
                    if not quote_data:
                        continue

                    current_price = quote_data.get('ltp', 0)
                    prev_close = quote_data.get('prev_close', 0)

                    if prev_close > 0:
                        gap_percent = ((current_price - prev_close) / prev_close) * 100

                        if gap_percent >= self.gap_threshold:
                            scanner_results.append({
                                'symbol': symbol,
                                'current_price': current_price,
                                'prev_close': prev_close,
                                'gap_percent': gap_percent,
                                'volume': quote_data.get('volume', 0),
                                'timestamp': datetime.now()
                            })

                except Exception as e:
                    self.logger.error(f"Error scanning {symbol}: {e}")
                    continue

            # Sort by gap percentage
            scanner_results.sort(key=lambda x: x['gap_percent'], reverse=True)
            self.scanner_symbols = scanner_results[:50]  # Keep top 50

            self.logger.info(f"Pre-market scanner found {len(scanner_results)} gap-up stocks")
            return scanner_results

        except Exception as e:
            self.logger.error(f"Pre-market scanner error: {e}")
            return []

    def verify_volume_threshold(self, symbol):
        """Verify if current volume meets threshold criteria"""
        try:
            if symbol not in self.historical_data:
                self.get_historical_data(symbol)

            df = self.historical_data.get(symbol)
            if df is None or len(df) < 20:
                return False

            # Calculate average daily volume
            avg_volume = df['volume'].tail(20).mean()

            # Get current volume
            current_data = self.live_data.get(symbol, {})
            current_volume = current_data.get('volume', 0)

            # Check if current volume >= 1.5x average
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            return volume_ratio >= self.volume_threshold

        except Exception as e:
            self.logger.error(f"Volume verification error for {symbol}: {e}")
            return False

    def check_entry_signals(self, symbol):
        """Check for entry signals based on RSI and price action"""
        try:
            # Get current indicators
            indicators = self.indicators.get(symbol, {})
            if not indicators:
                indicators = self.calculate_technical_indicators(symbol)
                if not indicators:
                    return None

            # Get current market data
            current_data = self.live_data.get(symbol, {})
            current_price = current_data.get('ltp', 0)

            if current_price == 0:
                return None

            # Get latest RSI value
            rsi_values = indicators.get('rsi', [])
            if len(rsi_values) < 2:
                return None

            current_rsi = rsi_values[-1]
            prev_rsi = rsi_values[-2]

            # Get VWAP
            vwap_values = indicators.get('vwap', [])
            current_vwap = vwap_values[-1] if len(vwap_values) > 0 else 0

            # Signal conditions
            rsi_signal = prev_rsi <= self.rsi_entry_level and current_rsi > self.rsi_entry_level
            price_above_vwap = current_price > current_vwap
            volume_confirmed = self.verify_volume_threshold(symbol)

            # Generate signal if all conditions met
            if rsi_signal and price_above_vwap and volume_confirmed:
                signal = {
                    'symbol': symbol,
                    'signal_type': 'BUY',
                    'price': current_price,
                    'rsi': current_rsi,
                    'vwap': current_vwap,
                    'volume_ratio': self.get_volume_ratio(symbol),
                    'timestamp': datetime.now(),
                    'confidence': self.calculate_signal_confidence(symbol)
                }

                self.signals.append(signal)
                self.logger.info(f"Entry signal generated for {symbol}: {signal}")

                return signal

            return None

        except Exception as e:
            self.logger.error(f"Signal check error for {symbol}: {e}")
            return None

    def calculate_signal_confidence(self, symbol):
        """Calculate confidence score for the signal"""
        try:
            confidence = 0

            # Volume factor (0-30 points)
            volume_ratio = self.get_volume_ratio(symbol)
            if volume_ratio >= 2.0:
                confidence += 30
            elif volume_ratio >= 1.5:
                confidence += 20
            elif volume_ratio >= 1.2:
                confidence += 10

            # RSI factor (0-25 points)
            indicators = self.indicators.get(symbol, {})
            rsi_values = indicators.get('rsi', [])
            if len(rsi_values) > 0:
                current_rsi = rsi_values[-1]
                if 60 <= current_rsi <= 70:
                    confidence += 25
                elif 55 <= current_rsi <= 75:
                    confidence += 15
                elif 50 <= current_rsi <= 80:
                    confidence += 10

            # Price action factor (0-25 points)
            current_data = self.live_data.get(symbol, {})
            change_percent = current_data.get('change_percent', 0)
            if change_percent >= 3:
                confidence += 25
            elif change_percent >= 2:
                confidence += 15
            elif change_percent >= 1:
                confidence += 10

            # Market timing factor (0-20 points)
            current_time = datetime.now().time()
            if datetime.strptime("09:15", "%H:%M").time() <= current_time <= datetime.strptime("15:30", "%H:%M").time():
                confidence += 20

            return min(confidence, 100)  # Cap at 100

        except Exception as e:
            self.logger.error(f"Confidence calculation error for {symbol}: {e}")
            return 0

    def get_volume_ratio(self, symbol):
        """Get current volume ratio vs average"""
        try:
            if symbol not in self.historical_data:
                return 0

            df = self.historical_data[symbol]
            avg_volume = df['volume'].tail(20).mean()

            current_data = self.live_data.get(symbol, {})
            current_volume = current_data.get('volume', 0)

            return current_volume / avg_volume if avg_volume > 0 else 0

        except Exception as e:
            return 0

    def get_quote_data(self, symbol):
        """Get current quote data for a symbol"""
        try:
            fyers_symbol = f"NSE:{symbol}-EQ" if not symbol.startswith("NSE:") else symbol
            response = self.fyers.quotes({"symbols": fyers_symbol})

            if response['s'] == 'ok' and 'd' in response:
                return response['d'][0]['v']
            return None

        except Exception as e:
            self.logger.error(f"Quote data error for {symbol}: {e}")
            return None

    def get_fno_symbols(self):
        """Get list of F&O symbols for scanning"""
        return [
            "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK",
            "HINDUNILVR", "SBIN", "BHARTIARTL", "KOTAKBANK", "ITC",
            "AXISBANK", "ASIANPAINT", "MARUTI", "TATAMOTORS", "SUNPHARMA",
            "WIPRO", "BAJFINANCE", "HCLTECH", "ADANIENT", "ULTRACEMCO",
            "BAJAJFINSV", "TITAN", "NTPC", "POWERGRID", "ONGC",
            "GRASIM", "INDUSINDBK", "JSWSTEEL", "ADANIPORTS", "HDFCLIFE"
        ]

    def get_dashboard_data(self):
        """Get comprehensive dashboard data"""
        return {
            'live_data': dict(self.live_data),
            'scanner_results': self.scanner_symbols,
            'signals': self.signals[-10:],  # Last 10 signals
            'monitored_symbols': list(self.monitored_symbols),
            'connection_status': self.is_connected,
            'timestamp': datetime.now().isoformat()
        }

    def start_monitoring(self, symbols=None):
        """Start live monitoring"""
        try:
            if symbols is None or len(symbols) == 0:
                symbols = self.get_fno_symbols()[:30]  # Monitor top 30 symbols by default
                self.logger.info(f"Starting monitoring with default symbols: {symbols[:10]}...")

            # Initialize WebSocket
            if self.initialize_websocket():
                # Subscribe to symbols
                self.subscribe_symbols(symbols)

                # Start WebSocket
                self.ws.connect()

                # Start scanner thread
                scanner_thread = threading.Thread(target=self.run_scanner_loop)
                scanner_thread.daemon = True
                scanner_thread.start()

                self.logger.info("Live monitoring started successfully")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
            return False

    def run_scanner_loop(self):
        """Run scanner in a loop (works anytime)"""
        while True:
            try:
                # Run scanner every 2 minutes (works anytime now)
                self.run_premarket_scanner()

                # Sleep for 2 minutes
                time.sleep(120)

            except Exception as e:
                self.logger.error(f"Scanner loop error: {e}")
                time.sleep(60)

    def stop_monitoring(self):
        """Stop live monitoring"""
        try:
            if self.ws:
                self.ws.disconnect()
            self.is_connected = False
            self.logger.info("Live monitoring stopped")

        except Exception as e:
            self.logger.error(f"Error stopping monitoring: {e}")
