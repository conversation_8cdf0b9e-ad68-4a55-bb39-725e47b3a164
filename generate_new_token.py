# Import required modules
from fyers_apiv3 import fyersModel
import hashlib
import requests
import json

# Your Fyers API credentials - Replace with your actual credentials
client_id = "YOUR_FYERS_CLIENT_ID_HERE"
secret_key = "YOUR_FYERS_SECRET_KEY_HERE"
redirect_uri = "https://www.google.co.in/"
response_type = "code"
state = "sample_state"

# Step 1: Generate the Authorization URL (visit this link to login)
session = fyersModel.SessionModel(
    client_id=client_id,
    secret_key=secret_key,
    redirect_uri=redirect_uri,
    response_type=response_type
)

auth_url = session.generate_authcode()
print("\n--- Authorization URL ---")
print("Visit this URL and login to get the 'code':\n")
print(auth_url)

# Step 2: Ask user to paste the received code
received_code = input("\nPaste the code you received after login: ").strip()

# Step 3: Generate appIdHash (SHA-256 of "app_id:secret_key")
def generate_appid_hash(app_id, secret_key):
    text = f"{app_id}:{secret_key}"
    return hashlib.sha256(text.encode('utf-8')).hexdigest()

app_id_hash = generate_appid_hash(client_id, secret_key)
print("\nGenerated appIdHash:", app_id_hash)

# Step 4: Make request to get access token
token_url = "https://api-t1.fyers.in/api/v3/validate-authcode"

payload = {
    "grant_type": "authorization_code",
    "appIdHash": app_id_hash,
    "code": received_code
}

headers = {
    "Content-Type": "application/json"
}

response = requests.post(token_url, headers=headers, data=json.dumps(payload))

print("\n--- Access Token Response ---")
print(response.json())
