services:
  - type: web
    name: options-analysis
    env: python
    buildCommand: chmod +x build.sh && ./build.sh
    startCommand: gunicorn app:app --log-file -
    envVars:
      - key: FLASK_ENV
        value: production
      - key: FLASK_SECRET_KEY
        generateValue: true
      - key: PYTHON_VERSION
        value: 3.11.8
      - key: FYERS_CLIENT_ID
        sync: false
      - key: FYERS_SECRET_KEY
        sync: false
      - key: FYERS_REDIRECT_URL
        sync: false
      - key: FYERS_ACCESS_TOKEN
        sync: false
