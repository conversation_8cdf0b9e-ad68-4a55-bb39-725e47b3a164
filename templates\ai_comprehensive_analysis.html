<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Trading Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1400px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 10px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            border-radius: 10px 10px 0 0 !important;
        }
        .ai-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .gamma-badge {
            background: linear-gradient(135deg, #00d2ff, #3a7bd5);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .analysis-mode-selector {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .mode-btn {
            border-radius: 25px;
            padding: 10px 20px;
            margin: 5px;
            border: 2px solid #dee2e6;
            background: white;
            transition: all 0.3s ease;
        }
        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        .mode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .trade-card {
            border-left: 5px solid #28a745;
            transition: all 0.3s ease;
        }
        .trade-card.put {
            border-left-color: #dc3545;
        }
        .trade-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        .confidence-bar {
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        .analysis-results {
            display: none;
        }
        .bullish { color: #28a745; }
        .bearish { color: #dc3545; }
        .neutral { color: #6c757d; }
        .high-confidence { background: linear-gradient(90deg, #28a745, #20c997); }
        .medium-confidence { background: linear-gradient(90deg, #ffc107, #fd7e14); }
        .low-confidence { background: linear-gradient(90deg, #dc3545, #e83e8c); }

        .ai-analysis-content {
            line-height: 1.6;
            color: #495057;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-robot me-2"></i>AI Trading Analysis
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/options-analysis">Traditional Analysis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/ai-comprehensive-analysis">AI Analysis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze-all">Batch Analysis</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-6 mb-3">
                    <i class="bi bi-cpu me-3"></i>AI-Powered Trading Analysis
                    <span class="ai-badge ms-3">Gemini 2.0</span>
                    <span class="gamma-badge ms-2">Gamma Acceleration</span>
                </h1>
                <p class="lead text-muted">
                    Advanced options analysis using AI with gamma acceleration trading strategy
                </p>
            </div>
        </div>

        <!-- Analysis Mode Selector -->
        <div class="analysis-mode-selector">
            <h5 class="mb-3">
                <i class="bi bi-gear me-2"></i>Analysis Mode
            </h5>
            <div class="d-flex flex-wrap">
                <button class="btn mode-btn active" id="singleStockMode" onclick="setAnalysisMode('single')">
                    <i class="bi bi-graph-up me-2"></i>Single Stock Analysis
                </button>
                <button class="btn mode-btn" id="batchAnalysisMode" onclick="setAnalysisMode('batch')">
                    <i class="bi bi-collection me-2"></i>Batch Analysis
                </button>
            </div>
        </div>

        <!-- Single Stock Analysis Section -->
        <div id="singleStockSection">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-search me-2"></i>Single Stock AI Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="symbolSelect" class="form-label">Symbol</label>
                                <select class="form-select" id="symbolSelect">
                                    <option value="">Select a symbol</option>
                                    <option value="NSE:NIFTY50-INDEX">NSE:NIFTY50-INDEX</option>
                                    <option value="NSE:FINNIFTY-INDEX">NSE:FINNIFTY-INDEX</option>
                                    <option value="NSE:MIDCPNIFTY-INDEX">NSE:MIDCPNIFTY-INDEX</option>
                                    <option value="NSE:RELIANCE-EQ">NSE:RELIANCE-EQ</option>
                                    <option value="NSE:TCS-EQ">NSE:TCS-EQ</option>
                                    <option value="NSE:HDFCBANK-EQ">NSE:HDFCBANK-EQ</option>
                                    <option value="NSE:INFY-EQ">NSE:INFY-EQ</option>
                                    <option value="NSE:ICICIBANK-EQ">NSE:ICICIBANK-EQ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="strikeCount" class="form-label">Strike Count</label>
                                <input type="number" class="form-control" id="strikeCount" value="20" min="5" max="50">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="expiryDate" class="form-label">Expiry Date</label>
                                <select class="form-select" id="expiryDate">
                                    <option value="">Auto-detect</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-primary btn-lg" id="analyzeSingleBtn" onclick="analyzeSingleStock()">
                            <i class="bi bi-cpu me-2"></i>Start AI Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Batch Analysis Section -->
        <div id="batchAnalysisSection" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-collection me-2"></i>Batch AI Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="watchlistSelect" class="form-label">Watchlist</label>
                                <select class="form-select" id="watchlistSelect">
                                    <option value="NIFTY50">Nifty 50</option>
                                    <option value="FNO">F&O Stocks</option>
                                    <option value="INDICES">Indices</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maxStocks" class="form-label">Max Stocks to Analyze</label>
                                <input type="number" class="form-control" id="maxStocks" value="10" min="5" max="50">
                            </div>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-success btn-lg" id="analyzeBatchBtn" onclick="analyzeBatch()">
                            <i class="bi bi-lightning me-2"></i>Start Batch AI Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 class="mt-3">AI is analyzing...</h5>
            <p class="text-muted">This may take a few moments</p>
        </div>

        <!-- Analysis Results -->
        <div class="analysis-results" id="analysisResults">
            <!-- Results will be populated here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentMode = 'single';
        let currentOptionsData = null;
        let currentPrice = 0;

        function setAnalysisMode(mode) {
            currentMode = mode;
            
            // Update button states
            document.getElementById('singleStockMode').classList.toggle('active', mode === 'single');
            document.getElementById('batchAnalysisMode').classList.toggle('active', mode === 'batch');
            
            // Show/hide sections
            document.getElementById('singleStockSection').style.display = mode === 'single' ? 'block' : 'none';
            document.getElementById('batchAnalysisSection').style.display = mode === 'batch' ? 'block' : 'none';
            
            // Clear previous results
            document.getElementById('analysisResults').style.display = 'none';
        }

        async function analyzeSingleStock() {
            const symbol = document.getElementById('symbolSelect').value;
            const strikeCount = document.getElementById('strikeCount').value;
            
            if (!symbol) {
                alert('Please select a symbol');
                return;
            }
            
            showLoading();
            
            try {
                // Step 1: Fetch option chain data
                console.log('Fetching option chain data...');
                const optionResponse = await fetch(`/api/fyers-option-chain?symbol=${encodeURIComponent(symbol)}&strike_count=${strikeCount}`);
                const optionData = await optionResponse.json();
                
                if (!optionData.success) {
                    throw new Error(optionData.message || 'Failed to fetch option chain data');
                }
                
                currentOptionsData = optionData.data;
                currentPrice = optionData.underlying_value;
                
                // Step 2: Run AI analysis
                console.log('Running AI analysis...');
                const analysisResponse = await fetch('/api/ai-single-stock-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        options_data: currentOptionsData,
                        current_price: currentPrice,
                        symbol: symbol,
                        technical_data: {} // Can be enhanced with real technical data
                    })
                });
                
                const analysisData = await analysisResponse.json();

                console.log('Raw analysis response:', analysisData);

                if (!analysisData.success) {
                    throw new Error(analysisData.message || 'AI analysis failed');
                }

                displaySingleStockResults(analysisData, symbol);
                
            } catch (error) {
                console.error('Error in single stock analysis:', error);
                alert('Error: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        async function analyzeBatch() {
            const watchlist = document.getElementById('watchlistSelect').value;
            const maxStocks = document.getElementById('maxStocks').value;

            showLoading();

            try {
                console.log('Starting batch analysis...');

                // Use the existing batch analysis API
                const response = await fetch('/api/batch-analysis/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        watchlist: watchlist,
                        expiry_date: null, // Auto-detect
                        settings: {
                            max_stocks: parseInt(maxStocks),
                            use_ai: true
                        }
                    })
                });

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.message || 'Failed to start batch analysis');
                }

                // Start polling for results
                currentTaskId = data.task_id;
                pollBatchResults();

            } catch (error) {
                console.error('Error in batch analysis:', error);
                alert('Error: ' + error.message);
                hideLoading();
            }
        }

        let currentTaskId = null;
        let pollInterval = null;

        function pollBatchResults() {
            if (!currentTaskId) return;

            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/batch-analysis/results?task_id=${currentTaskId}&count=10`);
                    const data = await response.json();

                    if (data.success) {
                        if (data.status === 'completed' || data.status === 'cancelled') {
                            clearInterval(pollInterval);
                            hideLoading();
                            displayBatchResults(data.results);
                        } else if (data.results && data.results.length > 0) {
                            // Show partial results
                            displayBatchResults(data.results);
                        }
                    }
                } catch (error) {
                    console.error('Error polling results:', error);
                    clearInterval(pollInterval);
                    hideLoading();
                }
            }, 2000); // Poll every 2 seconds
        }

        function displayBatchResults(results) {
            const resultsContainer = document.getElementById('analysisResults');

            if (!results || results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h5>No Results Found</h5>
                        <p>No trading opportunities were found in the selected watchlist. This could be due to:</p>
                        <ul>
                            <li>Low market volatility</li>
                            <li>Insufficient option liquidity</li>
                            <li>No clear trading signals</li>
                        </ul>
                    </div>
                `;
                resultsContainer.style.display = 'block';
                return;
            }

            let html = `
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-collection me-2"></i>Batch Analysis Results</h5>
                        <span class="badge bg-success">${results.length} Opportunities Found</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
            `;

            results.forEach((result, index) => {
                const trade = result.trade_recommendation;
                const symbol = result.symbol.replace('NSE:', '').replace('-EQ', '').replace('-INDEX', '');

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card trade-card ${trade.option_type.toLowerCase()}">
                            <div class="card-header">
                                <h6 class="mb-0">#${index + 1}: ${symbol} ${trade.option_type}</h6>
                                <span class="badge bg-primary">Score: ${Math.round(result.score)}/100</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Strike</small>
                                        <div class="fw-bold">₹${trade.strike}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Entry Price</small>
                                        <div class="fw-bold">₹${trade.buy_price.toFixed(2)}</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Target</small>
                                        <div class="fw-bold text-success">₹${trade.exit_target.toFixed(2)}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Stop Loss</small>
                                        <div class="fw-bold text-danger">₹${trade.stop_loss.toFixed(2)}</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Risk:Reward</small>
                                        <div class="fw-bold">1:${trade.risk_reward.toFixed(2)}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">IV</small>
                                        <div class="fw-bold">${trade.iv.toFixed(1)}%</div>
                                    </div>
                                </div>
                                ${trade.description ? `<p class="mt-2 small text-muted">${trade.description}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>
                </div>
            `;

            resultsContainer.innerHTML = html;
            resultsContainer.style.display = 'block';
        }

        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function displaySingleStockResults(data, symbol) {
            const resultsContainer = document.getElementById('analysisResults');
            const analysis = data.analysis;

            console.log('Displaying results for:', symbol);
            console.log('Analysis data:', analysis);

            // Check if analysis failed
            if (!analysis || !analysis.success) {
                resultsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Analysis Failed</h5>
                        <p>${analysis?.error || 'Unknown error occurred'}</p>
                    </div>
                `;
                resultsContainer.style.display = 'block';
                return;
            }

            // Handle case where analysis is raw JSON string
            if (typeof analysis === 'string') {
                try {
                    const parsedAnalysis = JSON.parse(analysis);
                    resultsContainer.innerHTML = formatAnalysisResults(data, parsedAnalysis, symbol);
                } catch (e) {
                    resultsContainer.innerHTML = formatRawAnalysisText(analysis, symbol);
                }
                resultsContainer.style.display = 'block';
                return;
            }

            // Display results with proper formatting
            resultsContainer.innerHTML = formatAnalysisResults(data, analysis, symbol);

            resultsContainer.style.display = 'block';
        }

        // Format analysis results with proper UI
        function formatAnalysisResults(data, analysis, symbol) {
            console.log('Formatting analysis results:', { data, analysis, symbol });

            // Handle case where analysis is raw text
            if (typeof analysis === 'string') {
                return formatRawAnalysisText(analysis, symbol);
            }

            // Handle case where analysis has ai_analysis_text (fallback from AI)
            if (analysis && analysis.ai_analysis_text) {
                return formatRawAnalysisText(analysis.ai_analysis_text, symbol);
            }

            if (!analysis || !analysis.success) {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-exclamation-triangle me-2"></i>Analysis Error</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <p>Analysis failed: ${analysis?.error || 'Unknown error'}</p>
                            </div>
                        </div>
                    </div>
                `;
            }

            let html = `
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-check-circle me-2"></i>AI Analysis Results for ${symbol}</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>Analysis Completed Successfully!</h6>
                            <p><strong>Strategy:</strong> ${data.strategy}</p>
                            <p><strong>Analysis Type:</strong> ${data.analysis_type}</p>
                            <p><strong>Confidence Level:</strong> ${analysis.confidence_level || 'N/A'}</p>
                        </div>
                    </div>
                </div>
            `;

            // Gamma Acceleration Analysis
            if (analysis.gamma_acceleration_analysis) {
                const gamma = analysis.gamma_acceleration_analysis;
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-lightning me-2"></i>Gamma Acceleration Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>Liquidity Score</h6>
                                    <div class="h4 text-primary">${gamma.liquidity_score || 'N/A'}/10</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Momentum Score</h6>
                                    <div class="h4 text-success">${gamma.momentum_score || 'N/A'}/10</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Suitable Options</h6>
                                    <div class="h4 text-info">${gamma.suitable_options?.length || 0}</div>
                                </div>
                            </div>
                            ${formatSuitableOptions(gamma.suitable_options)}
                        </div>
                    </div>
                `;
            }

            // Max Pain Analysis
            if (analysis.max_pain_analysis) {
                const maxPain = analysis.max_pain_analysis;
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-target me-2"></i>Max Pain Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Max Pain Strike</h6>
                                    <div class="h4 text-warning">₹${maxPain.max_pain_strike || 'N/A'}</div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Directional Bias</h6>
                                    <div class="h4 ${getBiasClass(maxPain.bias)}">${maxPain.bias || 'Neutral'}</div>
                                </div>
                            </div>
                            ${maxPain.analysis ? `<p class="mt-3">${maxPain.analysis}</p>` : ''}
                        </div>
                    </div>
                `;
            }

            // PCR Analysis
            if (analysis.pcr_analysis) {
                const pcr = analysis.pcr_analysis;
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-bar-chart me-2"></i>Put-Call Ratio Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>PCR (OI)</h6>
                                    <div class="h4 text-primary">${pcr.pcr_oi?.toFixed(2) || 'N/A'}</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>PCR (Volume)</h6>
                                    <div class="h4 text-success">${pcr.pcr_volume?.toFixed(2) || 'N/A'}</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Bias</h6>
                                    <div class="h4 ${getBiasClass(pcr.bias)}">${pcr.bias || 'Neutral'}</div>
                                </div>
                            </div>
                            ${pcr.interpretation ? `<p class="mt-3">${pcr.interpretation}</p>` : ''}
                        </div>
                    </div>
                `;
            }

            // Technical Analysis
            if (analysis.technical_analysis) {
                const tech = analysis.technical_analysis;
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-graph-up me-2"></i>Technical Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>RSI Signal</h6>
                                    <div class="h5 ${getBiasClass(tech.rsi_signal)}">${tech.rsi_signal || 'N/A'}</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>ADX Strength</h6>
                                    <div class="h5 text-info">${tech.adx_strength || 'N/A'}</div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Trend Direction</h6>
                                    <div class="h5 text-secondary">${tech.trend_direction || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Trade Recommendations
            if (analysis.trade_recommendations && analysis.trade_recommendations.length > 0) {
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-bullseye me-2"></i>Trade Recommendations</h5>
                        </div>
                        <div class="card-body">
                            ${formatTradeRecommendations(analysis.trade_recommendations)}
                        </div>
                    </div>
                `;
            }

            return html;
        }

        // Format suitable options
        function formatSuitableOptions(options) {
            if (!options || options.length === 0) {
                return '<p class="text-muted mt-3">No suitable options found for gamma acceleration strategy.</p>';
            }

            let html = '<div class="mt-3"><h6>Suitable Options:</h6><div class="row">';

            options.forEach((option, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">${option.type} - ₹${option.strike}</h6>
                                <p class="card-text">
                                    <strong>Premium:</strong> ₹${option.premium}<br>
                                    <strong>OTM %:</strong> ${option.otm_percentage}%<br>
                                    <strong>Volume:</strong> ${option.volume?.toLocaleString() || 'N/A'}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';
            return html;
        }

        // Format trade recommendations
        function formatTradeRecommendations(recommendations) {
            let html = '<div class="row">';

            recommendations.forEach((rec, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">${rec.type} Option - ₹${rec.strike}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Entry Price</small>
                                        <div class="fw-bold">₹${rec.entry_price}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Target</small>
                                        <div class="fw-bold text-success">₹${rec.target}</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Stop Loss</small>
                                        <div class="fw-bold text-danger">₹${rec.stop_loss}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Gamma Score</small>
                                        <div class="fw-bold text-primary">${rec.gamma_score}/10</div>
                                    </div>
                                </div>
                                ${rec.rationale ? `<p class="mt-3 small text-muted">${rec.rationale}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        // Format raw analysis text when structured data is not available
        function formatRawAnalysisText(text, symbol) {
            console.log('Formatting raw analysis text:', text);

            // Try to parse as JSON first
            try {
                const parsed = JSON.parse(text);
                console.log('Successfully parsed JSON:', parsed);
                return formatAnalysisResults({strategy: 'Gamma Acceleration', analysis_type: 'AI Single Stock Analysis'}, parsed, symbol);
            } catch (e) {
                console.log('Not JSON, formatting as text');

                // Extract key information from text using regex
                const extractedInfo = extractAnalysisInfo(text);

                // Format as structured display
                let html = `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-check-circle me-2"></i>AI Analysis Results for ${symbol}</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6>Analysis Completed Successfully!</h6>
                                <p><strong>Strategy:</strong> Gamma Acceleration</p>
                                <p><strong>Analysis Type:</strong> AI Single Stock Analysis</p>
                            </div>
                `;

                // Add extracted information if available
                if (extractedInfo.maxPain) {
                    html += `
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="bi bi-target me-2"></i>Max Pain Analysis</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Max Pain Strike:</strong> ${extractedInfo.maxPain}</p>
                            </div>
                        </div>
                    `;
                }

                if (extractedInfo.pcr) {
                    html += `
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="bi bi-bar-chart me-2"></i>Put-Call Ratio</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>PCR:</strong> ${extractedInfo.pcr}</p>
                            </div>
                        </div>
                    `;
                }

                // Add the full formatted text
                const formattedText = text
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/₹([\d,]+)/g, '<span class="text-success fw-bold">₹$1</span>')
                    .replace(/(\d+\.?\d*)%/g, '<span class="text-primary fw-bold">$1%</span>')
                    .replace(/(Call|Put)/gi, '<span class="badge bg-info">$1</span>')
                    .replace(/(Bullish|Bearish|Neutral)/gi, '<span class="badge bg-secondary">$1</span>');

                html += `
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="bi bi-robot me-2"></i>AI Analysis Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="ai-analysis-content">
                                        ${formattedText}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                return html;
            }
        }

        // Extract key information from analysis text
        function extractAnalysisInfo(text) {
            const info = {};

            // Extract Max Pain
            const maxPainMatch = text.match(/max pain[:\s]*₹?(\d+)/i);
            if (maxPainMatch) {
                info.maxPain = `₹${maxPainMatch[1]}`;
            }

            // Extract PCR
            const pcrMatch = text.match(/pcr[:\s]*(\d+\.?\d*)/i);
            if (pcrMatch) {
                info.pcr = pcrMatch[1];
            }

            // Extract bias
            const biasMatch = text.match(/(bullish|bearish|neutral)/i);
            if (biasMatch) {
                info.bias = biasMatch[1];
            }

            return info;
        }

        // Get CSS class for bias
        function getBiasClass(bias) {
            if (!bias) return 'text-muted';
            const lowerBias = bias.toLowerCase();
            if (lowerBias.includes('bullish')) return 'text-success';
            if (lowerBias.includes('bearish')) return 'text-danger';
            return 'text-warning';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI Comprehensive Analysis page loaded');
        });
    </script>
</body>
</html>
