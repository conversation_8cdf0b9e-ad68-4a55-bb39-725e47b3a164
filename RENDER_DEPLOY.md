# Manual Deployment Guide for Render

This guide provides step-by-step instructions for manually deploying the application on Render without using Blueprints.

## Prerequisites

1. A Render account
2. Your GitHub repository with the code
3. Fyers API credentials

## Deployment Steps

### 1. Create a New Web Service

1. Log in to your Render dashboard
2. Click on "New" in the top right corner
3. Select "Web Service"

### 2. Connect Your Repository

1. Choose "GitHub" as the source
2. Connect your GitHub account if not already connected
3. Select the repository containing your code
4. Click "Connect"

### 3. Configure the Web Service

Enter the following configuration:

- **Name**: options-analysis (or your preferred name)
- **Environment**: Python
- **Region**: Choose a region close to your users
- **Branch**: master (or your main branch)
- **Build Command**: `chmod +x build.sh && ./build.sh`
- **Start Command**: `gunicorn app:app --log-file -`

### 4. Set Environment Variables

Add the following environment variables:

- `FLASK_ENV`: production
- `FLASK_SECRET_KEY`: (generate a random string)
- `PYTHON_VERSION`: 3.11.8
- `FYERS_CLIENT_ID`: (your Fyers client ID)
- `FYERS_SECRET_KEY`: (your Fyers secret key)
- `FYERS_REDIRECT_URL`: (your Fyers redirect URL)
- `FYERS_ACCESS_TOKEN`: (your Fyers access token)

### 5. Deploy the Service

1. Click "Create Web Service"
2. Wait for the deployment to complete (this may take a few minutes)
3. Once deployed, you'll see a URL where your application is accessible

## Updating the Access Token

The Fyers API access token expires daily. To update it:

1. Go to the Render dashboard
2. Navigate to your web service
3. Go to the Environment tab
4. Update the `FYERS_ACCESS_TOKEN` value
5. Click "Save Changes"

## Troubleshooting

If you encounter any issues during deployment:

1. Check the build logs for specific error messages
2. Ensure all environment variables are set correctly
3. Verify that your repository contains all the necessary files (build.sh, Procfile, etc.)
4. Try redeploying the service after making any changes

For more help, refer to the [Render documentation](https://render.com/docs) or contact Render support.
