import os
import json
import time
import jwt
from datetime import datetime, timed<PERSON>ta
from fyers_apiv3 import fyersModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class FyersTokenManager:
    """Comprehensive token manager for Fyers API"""
    
    def __init__(self):
        self.app_id = os.environ.get('FYERS_APP_ID')
        self.secret_id = os.environ.get('FYERS_SECRET_ID')
        self.client_id = os.environ.get('FYERS_CLIENT_ID')
        self.redirect_uri = "https://www.google.co.in/"
        self.token_file = os.path.join('tokens', 'fyers_token.json')

        # Ensure tokens directory exists
        os.makedirs('tokens', exist_ok=True)

        # Validate required credentials
        if not all([self.app_id, self.secret_id, self.client_id]):
            print("Warning: Missing required Fyers API credentials in environment variables")
            print("Please set FYERS_APP_ID, FYERS_SECRET_ID, and FYERS_CLIENT_ID")
    
    def get_auth_url(self):
        """Generate authorization URL for Fyers login"""
        try:
            session = fyersModel.SessionModel(
                client_id=self.app_id,
                secret_key=self.secret_id,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="authorization_code"
            )
            
            auth_url = session.generate_authcode()
            return {"success": True, "auth_url": auth_url}
            
        except Exception as e:
            return {"success": False, "message": f"Error generating auth URL: {str(e)}"}
    
    def generate_access_token(self, auth_code):
        """Generate access token from auth code"""
        try:
            session = fyersModel.SessionModel(
                client_id=self.app_id,
                secret_key=self.secret_id,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="authorization_code"
            )
            
            session.set_token(auth_code)
            response = session.generate_token()
            
            if response and response.get('s') == 'ok':
                access_token = response.get('access_token')
                refresh_token = response.get('refresh_token')
                
                if access_token:
                    # Save tokens
                    token_data = {
                        "access_token": access_token,
                        "refresh_token": refresh_token,
                        "timestamp": time.time(),
                        "expires_at": time.time() + (24 * 60 * 60)  # 24 hours
                    }
                    
                    with open(self.token_file, 'w') as f:
                        json.dump(token_data, f)
                    
                    # Update environment variable for current session
                    os.environ['FYERS_ACCESS_TOKEN'] = access_token
                    if refresh_token:
                        os.environ['FYERS_REFRESH_TOKEN'] = refresh_token
                    
                    return {
                        "success": True,
                        "message": "Access token generated successfully",
                        "access_token": access_token
                    }
                else:
                    return {"success": False, "message": "No access token in response"}
            else:
                error_msg = response.get('message', 'Unknown error') if response else 'No response from API'
                return {"success": False, "message": f"Token generation failed: {error_msg}"}
                
        except Exception as e:
            return {"success": False, "message": f"Error generating token: {str(e)}"}
    
    def is_token_expired(self, token_data):
        """Check if token is expired"""
        if not token_data:
            return True
            
        expires_at = token_data.get('expires_at', 0)
        current_time = time.time()
        
        # Consider token expired if it expires within next 30 minutes
        return current_time >= (expires_at - 1800)
    
    def decode_token_info(self, token):
        """Decode JWT token to get expiration info"""
        try:
            # Decode without verification to get payload
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get('exp', 0)
            
            if exp_timestamp:
                exp_datetime = datetime.fromtimestamp(exp_timestamp)
                return {
                    "expires_at": exp_timestamp,
                    "expires_datetime": exp_datetime,
                    "is_expired": time.time() > exp_timestamp
                }
            return None
        except Exception:
            return None
    
    def refresh_access_token(self):
        """Refresh access token using refresh token"""
        try:
            # Get refresh token from environment or file
            refresh_token = os.environ.get('FYERS_REFRESH_TOKEN')
            
            if not refresh_token and os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)
                refresh_token = token_data.get('refresh_token')
            
            if not refresh_token:
                return {"success": False, "message": "No refresh token available"}
            
            # Use refresh token to get new access token
            session = fyersModel.SessionModel(
                client_id=self.app_id,
                secret_key=self.secret_id,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="refresh_token"
            )
            
            session.set_token(refresh_token)
            response = session.generate_token()
            
            if response and response.get('s') == 'ok':
                access_token = response.get('access_token')
                new_refresh_token = response.get('refresh_token', refresh_token)
                
                if access_token:
                    # Save new tokens
                    token_data = {
                        "access_token": access_token,
                        "refresh_token": new_refresh_token,
                        "timestamp": time.time(),
                        "expires_at": time.time() + (24 * 60 * 60)
                    }
                    
                    with open(self.token_file, 'w') as f:
                        json.dump(token_data, f)
                    
                    # Update environment variables
                    os.environ['FYERS_ACCESS_TOKEN'] = access_token
                    os.environ['FYERS_REFRESH_TOKEN'] = new_refresh_token
                    
                    return {
                        "success": True,
                        "message": "Token refreshed successfully",
                        "access_token": access_token
                    }
            
            return {"success": False, "message": "Failed to refresh token"}
            
        except Exception as e:
            return {"success": False, "message": f"Error refreshing token: {str(e)}"}
    
    def get_valid_access_token(self):
        """Get a valid access token, refreshing if necessary"""
        try:
            # First try environment variable
            access_token = os.environ.get('FYERS_ACCESS_TOKEN')
            
            # If not in environment, try file
            if not access_token and os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)
                access_token = token_data.get('access_token')
                
                # Check if token is expired
                if access_token and self.is_token_expired(token_data):
                    print("Token expired, attempting to refresh...")
                    refresh_result = self.refresh_access_token()
                    if refresh_result.get('success'):
                        access_token = refresh_result.get('access_token')
                    else:
                        print(f"Token refresh failed: {refresh_result.get('message')}")
                        return None
            
            return access_token
            
        except Exception as e:
            print(f"Error getting valid access token: {str(e)}")
            return None
    
    def save_manual_token(self, access_token):
        """Save manually provided access token"""
        try:
            # Validate token format and get expiration info
            token_info = self.decode_token_info(access_token)
            
            token_data = {
                "access_token": access_token,
                "timestamp": time.time(),
                "expires_at": token_info.get('expires_at', time.time() + (24 * 60 * 60)) if token_info else time.time() + (24 * 60 * 60)
            }
            
            with open(self.token_file, 'w') as f:
                json.dump(token_data, f)
            
            # Update environment variable
            os.environ['FYERS_ACCESS_TOKEN'] = access_token
            
            return {"success": True, "message": "Token saved successfully"}
            
        except Exception as e:
            return {"success": False, "message": f"Error saving token: {str(e)}"}
    
    def get_token_status(self):
        """Get current token status and information"""
        try:
            access_token = self.get_valid_access_token()
            
            if not access_token:
                return {
                    "has_token": False,
                    "is_valid": False,
                    "message": "No access token found"
                }
            
            # Get token info
            token_info = self.decode_token_info(access_token)
            
            if token_info:
                return {
                    "has_token": True,
                    "is_valid": not token_info.get('is_expired', True),
                    "expires_at": token_info.get('expires_datetime'),
                    "token_preview": access_token[:20] + "..." if len(access_token) > 20 else access_token
                }
            else:
                return {
                    "has_token": True,
                    "is_valid": True,  # Assume valid if we can't decode
                    "message": "Token format could not be decoded",
                    "token_preview": access_token[:20] + "..." if len(access_token) > 20 else access_token
                }
                
        except Exception as e:
            return {
                "has_token": False,
                "is_valid": False,
                "message": f"Error checking token status: {str(e)}"
            }
