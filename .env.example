# Flask Configuration
FLASK_ENV=development
# Generate a random secret key for Flask sessions (use a long random string)
# You can generate one using: python -c "import secrets; print(secrets.token_hex(32))"
FLASK_SECRET_KEY=your_random_secret_key_here_64_characters_long_example_1234567890abcdef

# Fyers API Configuration
# Get these from your Fyers API Dashboard: https://api-dashboard.fyers.in/
# Format: APPID-100 (e.g., ABC12345-100)
FYERS_APP_ID=YOUR_APP_ID-100
FYERS_SECRET_ID=YOUR_SECRET_ID
FYERS_CLIENT_ID=YOUR_APP_ID-100
# These tokens are generated through the authentication process
FYERS_REFRESH_TOKEN=your_refresh_token_from_authentication
FYERS_ACCESS_TOKEN=your_access_token_from_authentication

# Google Gemini AI Configuration (Optional - for AI features)
# Get your API key from: https://makersuite.google.com/app/apikey
# Format: AIzaSy... (39 characters)
GEMINI_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Application Settings
DEBUG=True
