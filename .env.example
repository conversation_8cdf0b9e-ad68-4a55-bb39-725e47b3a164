# Flask Configuration
FLASK_ENV=development
# Generate a random secret key for Flask sessions (use a long random string)
# You can generate one using: python -c "import secrets; print(secrets.token_hex(32))"
FLASK_SECRET_KEY=b627f153060dd4be73feb1968e38872354ed6dd2986093ecad39432749ebde80

# Fyers API Configuration
# Get these from your Fyers API Dashboard: https://api-dashboard.fyers.in/
# Format: APPID-100 (e.g., 9JGVOKPK31-100)
FYERS_APP_ID=9JGVOKPK31-100
FYERS_SECRET_ID=5CGA1HSMOP
FYERS_CLIENT_ID=9JGVOKPK31-100
FYERS_REDIRECT_URL=https://www.google.co.in/
# These tokens are generated through the authentication process - use Token Manager to generate
FYERS_REFRESH_TOKEN=your_refresh_token_from_token_manager
FYERS_ACCESS_TOKEN=your_access_token_from_token_manager

# Google Gemini AI Configuration (Optional - for AI features)
# Get your API key from: https://makersuite.google.com/app/apikey
# Format: AIzaSy... (39 characters)
GEMINI_API_KEY=AIzaSyAuElFYg5P4CICGcjsy94t6Ndmh1OAfFZU

# Application Settings
DEBUG=True

# Additional Flask Settings
SESSION_TYPE=filesystem
PERMANENT_SESSION_LIFETIME=7200

# File Upload Settings
UPLOAD_FOLDER=uploads
OUTPUT_FOLDER=outputs
MAX_CONTENT_LENGTH=16777216

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=app.log

# Security Settings
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600
