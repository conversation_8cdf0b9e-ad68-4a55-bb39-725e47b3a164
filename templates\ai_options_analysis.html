<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Options Analysis - Fyers Integration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .analysis-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .ai-badge {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .traditional-badge {
            background: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .trade-recommendation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .confidence-bar {
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .high-confidence { background: #28a745; }
        .medium-confidence { background: #ffc107; }
        .low-confidence { background: #dc3545; }

        /* Enhanced AI Insights Styling */
        .ai-insights-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .insight-section {
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-header {
            background: linear-gradient(90deg, #f8f9fa, #e9ecef);
            padding: 12px 16px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
        }

        .section-header h6 {
            color: #495057;
            font-weight: 600;
        }

        .section-content {
            padding: 16px;
        }

        .insight-text {
            line-height: 1.6;
            margin-bottom: 0;
            color: #495057;
        }

        .key-points {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .key-point {
            display: flex;
            align-items: flex-start;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }

        /* Highlighting Styles */
        .sentiment-highlight {
            background: linear-gradient(120deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
        }

        .metric-highlight {
            background: linear-gradient(120deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .level-highlight {
            background: linear-gradient(120deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .price-highlight {
            background: linear-gradient(120deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            font-family: 'Courier New', monospace;
        }

        .percentage-highlight {
            background: linear-gradient(120deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        /* Trade Recommendation Enhancements */
        .trade-recommendation {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .trade-recommendation h6 {
            color: #495057;
            margin-bottom: 10px;
        }

        /* AI Text Content Formatting */
        .ai-text-content {
            line-height: 1.6;
            color: #495057;
        }

        .ai-raw-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
        }

        .trade-rec-item {
            background: #f8f9fa;
            font-size: 0.9em;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .ai-insights-container {
                padding: 15px;
            }

            .section-content {
                padding: 12px;
            }

            .key-point {
                padding: 6px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="/">
                            <i class="fas fa-chart-line"></i> Fyers AI Options Analysis
                        </a>
                        <div class="navbar-nav ms-auto">
                            <a class="nav-link" href="/">Home</a>
                            <a class="nav-link" href="/options-analysis">Traditional Analysis</a>
                            <a class="nav-link active" href="/ai-options-analysis">AI Analysis</a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-robot"></i> AI-Powered Options Analysis 
                            <span class="ai-badge">Gemini 2.0</span>
                        </h4>
                        <p class="mb-0">Advanced options analysis using Google's Gemini 2.0 Flash model with gamma acceleration strategy</p>
                    </div>
                    <div class="card-body">
                        <!-- Symbol Selection -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="symbolSelect" class="form-label">Select Symbol:</label>
                                <select class="form-select" id="symbolSelect">
                                    <option value="">Choose a symbol...</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="strikeCount" class="form-label">Strike Count:</label>
                                <select class="form-select" id="strikeCount">
                                    <option value="5">5 Strikes</option>
                                    <option value="10" selected>10 Strikes</option>
                                    <option value="15">15 Strikes</option>
                                    <option value="20">20 Strikes</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisType" class="form-label">Analysis Type:</label>
                                <select class="form-select" id="analysisType">
                                    <option value="ai_only">AI Only</option>
                                    <option value="comparison" selected>AI + Traditional Comparison</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="fetchAndAnalyze()">
                                    <i class="fas fa-search"></i> Analyze
                                </button>
                            </div>
                        </div>

                        <!-- Loading Spinner -->
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Fetching data and running AI analysis...</p>
                        </div>

                        <!-- Results Container -->
                        <div id="resultsContainer" style="display: none;">
                            <!-- Market Data Summary -->
                            <div class="card analysis-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar"></i> Market Data Summary</h5>
                                </div>
                                <div class="card-body" id="marketDataSummary">
                                    <!-- Market data will be populated here -->
                                </div>
                            </div>

                            <!-- Analysis Results -->
                            <div class="comparison-container" id="analysisResults">
                                <!-- AI and Traditional analysis results will be populated here -->
                            </div>

                            <!-- Trade Recommendations -->
                            <div class="card analysis-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-bullseye"></i> AI Trade Recommendations</h5>
                                </div>
                                <div class="card-body" id="tradeRecommendations">
                                    <!-- Trade recommendations will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Error Container -->
                        <div id="errorContainer" style="display: none;">
                            <div class="alert alert-danger" role="alert">
                                <h4 class="alert-heading">Analysis Error</h4>
                                <p id="errorMessage"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentOptionsData = null;
        let currentUnderlyingValue = null;
        let currentSymbol = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();
        });

        // Load available symbols
        function loadSymbols() {
            fetch('/api/fyers-stocks')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('symbolSelect');
                        data.stocks.forEach(symbol => {
                            const option = document.createElement('option');
                            option.value = symbol;
                            option.textContent = symbol;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading symbols:', error);
                });
        }

        // Main analysis function
        async function fetchAndAnalyze() {
            const symbol = document.getElementById('symbolSelect').value;
            const strikeCount = document.getElementById('strikeCount').value;
            const analysisType = document.getElementById('analysisType').value;

            if (!symbol) {
                alert('Please select a symbol');
                return;
            }

            // Show loading
            showLoading();
            hideResults();
            hideError();

            try {
                // Step 1: Fetch option chain data
                console.log(`Fetching option chain for ${symbol}...`);
                const optionChainResponse = await fetch(`/api/fyers-option-chain?symbol=${encodeURIComponent(symbol)}&strike_count=${strikeCount}`);
                const optionChainData = await optionChainResponse.json();

                if (!optionChainData.success) {
                    throw new Error(optionChainData.message || 'Failed to fetch option chain data');
                }

                // Store data globally
                currentOptionsData = optionChainData.data;
                currentUnderlyingValue = optionChainData.underlying_value;
                currentSymbol = symbol;

                // Step 2: Run AI analysis
                console.log('Running AI analysis...');
                const analysisResponse = await fetch('/api/ai-options-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        options_data: currentOptionsData,
                        current_price: currentUnderlyingValue,
                        symbol: symbol,
                        days_to_expiry: null // Will be calculated from expiry date
                    })
                });

                const analysisData = await analysisResponse.json();

                if (!analysisData.success) {
                    throw new Error(analysisData.message || 'AI analysis failed');
                }

                // Display results
                displayResults(analysisData, optionChainData, analysisType);

            } catch (error) {
                console.error('Analysis error:', error);
                showError(error.message);
            } finally {
                hideLoading();
            }
        }

        // Display analysis results
        function displayResults(analysisData, optionChainData, analysisType) {
            // Show market data summary
            displayMarketDataSummary(optionChainData);

            // Show analysis results based on type
            if (analysisType === 'comparison') {
                displayComparisonResults(analysisData);
            } else {
                displayAIOnlyResults(analysisData.ai_analysis);
            }

            // Show trade recommendations
            displayTradeRecommendations(analysisData.ai_analysis);

            showResults();
        }

        // Display market data summary
        function displayMarketDataSummary(data) {
            const container = document.getElementById('marketDataSummary');
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <h6>Symbol</h6>
                        <p class="h5 text-primary">${currentSymbol}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Current Price</h6>
                        <p class="h5 text-success">₹${data.underlying_value}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Options Count</h6>
                        <p class="h5">${data.data.length}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Analysis Time</h6>
                        <p class="h5">${data.timestamp}</p>
                    </div>
                </div>
                ${data.has_real_oi_data ? 
                    '<div class="alert alert-success"><i class="fas fa-check"></i> Real OI data available</div>' : 
                    '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Limited OI data - results may be less accurate</div>'
                }
            `;
        }

        // Display comparison results (AI vs Traditional)
        function displayComparisonResults(data) {
            const container = document.getElementById('analysisResults');
            container.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-robot"></i> AI Analysis <span class="ai-badge">Gemini 2.0</span></h5>
                    </div>
                    <div class="card-body">
                        ${formatAIAnalysis(data.ai_analysis)}
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator"></i> Traditional Analysis <span class="traditional-badge">Mathematical</span></h5>
                    </div>
                    <div class="card-body">
                        ${formatTraditionalAnalysis(data.traditional_analysis)}
                    </div>
                </div>
            `;
        }

        // Display AI-only results
        function displayAIOnlyResults(aiData) {
            const container = document.getElementById('analysisResults');
            container.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-robot"></i> AI Analysis <span class="ai-badge">Gemini 2.0</span></h5>
                        <p class="mb-0 text-muted">Advanced AI-powered analysis with live market calculations</p>
                    </div>
                    <div class="card-body">
                        ${formatAIAnalysis(aiData)}
                    </div>
                </div>
            `;
        }

        // Format AI analysis results with live calculations
        function formatAIAnalysis(aiData) {
            if (!aiData || !aiData.success) {
                return `<div class="alert alert-danger">AI Analysis Failed: ${aiData?.error || 'Unknown error'}</div>`;
            }

            // Handle case where AI returns raw text instead of structured data
            if (typeof aiData === 'string') {
                return `
                    <div class="alert alert-info">
                        <h6>AI Analysis Results</h6>
                        <div class="ai-text-content">${formatAITextContent(aiData)}</div>
                    </div>
                `;
            }

            let html = '';

            // Analysis type indicator
            const analysisType = aiData.metadata?.analysis_type || 'unknown';
            const isLiveAnalysis = analysisType.includes('live_calculations');

            if (isLiveAnalysis) {
                html += `
                    <div class="alert alert-success mb-3">
                        <i class="fas fa-chart-line"></i> <strong>Live Analysis Active</strong> - Using real-time calculations with ${aiData.metadata?.data_points_analyzed || 0} option data points
                    </div>
                `;
            }

            // Market Sentiment (from live calculations)
            if (aiData.market_sentiment) {
                const sentiment = aiData.market_sentiment;
                const sentimentClass = sentiment.overall_sentiment === 'Bullish' ? 'text-success' :
                                     sentiment.overall_sentiment === 'Bearish' ? 'text-danger' : 'text-warning';

                html += `
                    <div class="mb-3">
                        <h6>Market Sentiment (Live)</h6>
                        <p class="h5 ${sentimentClass}">${sentiment.overall_sentiment}</p>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${sentiment.confidence_level}%; background: ${sentiment.overall_sentiment === 'Bullish' ? '#28a745' : sentiment.overall_sentiment === 'Bearish' ? '#dc3545' : '#ffc107'}"></div>
                        </div>
                        <small>Confidence: ${sentiment.confidence_level}%</small>
                        <p class="mt-2 text-muted">${sentiment.interpretation}</p>
                    </div>
                `;
            }

            // Live PCR Analysis
            if (aiData.pcr_analysis) {
                const pcr = aiData.pcr_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Put-Call Ratio (Live)</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>PCR (OI):</strong> ${pcr.pcr_oi || 'N/A'}
                            </div>
                            <div class="col-md-4">
                                <strong>PCR (Volume):</strong> ${pcr.pcr_volume || 'N/A'}
                            </div>
                            <div class="col-md-4">
                                <strong>Signal:</strong> ${pcr.signal_strength || 'N/A'}
                            </div>
                        </div>
                        <p class="mt-2">${pcr.interpretation || 'No interpretation available'}</p>
                    </div>
                `;
            }

            // Live Max Pain Analysis
            if (aiData.max_pain_analysis) {
                const maxPain = aiData.max_pain_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Max Pain Analysis (Live)</h6>
                        <p><strong>Max Pain Strike:</strong> ₹${maxPain.max_pain_strike || 'N/A'}</p>
                        <p><strong>Current vs Max Pain:</strong> ${Math.abs((maxPain.max_pain_strike || 0) - (aiData.current_price || 0)).toFixed(2)} points difference</p>
                    </div>
                `;
            }

            // Greeks Analysis (if available)
            if (aiData.greeks_analysis && !aiData.greeks_analysis.error) {
                const greeks = aiData.greeks_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Greeks Analysis (Live)</h6>
                        <p><strong>Total Gamma Exposure:</strong> ${greeks.total_gamma_exposure || 'N/A'}</p>
                        <p>${greeks.gamma_exposure_interpretation || ''}</p>
                    </div>
                `;
            }

            // AI Insights removed as per user request - no need to show to users

            // Data Quality Assessment
            if (aiData.data_quality) {
                const quality = aiData.data_quality;
                const qualityClass = quality.quality_rating === 'High' ? 'success' :
                                   quality.quality_rating === 'Medium' ? 'warning' : 'danger';

                html += `
                    <div class="mb-3">
                        <h6>Data Quality</h6>
                        <span class="badge bg-${qualityClass}">${quality.quality_rating} (${quality.quality_score}%)</span>
                        <small class="d-block mt-1">${quality.total_options} options analyzed</small>
                    </div>
                `;
            }

            return html || '<p>AI analysis data not available in expected format.</p>';
        }

        // Format AI insights with proper sections and highlighting
        function formatAIInsights(aiInsights) {
            if (!aiInsights || aiInsights === 'AI insights unavailable') {
                return '';
            }

            // Parse the AI insights text into sections
            const sections = parseAIInsights(aiInsights);

            let html = `
                <div class="ai-insights-container">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-robot text-primary me-2"></i>
                        <h5 class="mb-0">AI Strategic Insights</h5>
                        <span class="badge bg-success ms-2">Gemini 2.0</span>
                    </div>
            `;

            // Validation Section
            if (sections.validation) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <h6 class="mb-0">Analysis Validation</h6>
                        </div>
                        <div class="section-content">
                            ${formatInsightContent(sections.validation)}
                        </div>
                    </div>
                `;
            }

            // Strategic Insights Section
            if (sections.strategic) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <h6 class="mb-0">Strategic Insights</h6>
                        </div>
                        <div class="section-content">
                            ${formatInsightContent(sections.strategic)}
                        </div>
                    </div>
                `;
            }

            // Risk Assessment Section
            if (sections.risk) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            <h6 class="mb-0">Risk Assessment</h6>
                        </div>
                        <div class="section-content">
                            ${formatInsightContent(sections.risk)}
                        </div>
                    </div>
                `;
            }

            // Market Outlook Section
            if (sections.outlook) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-chart-line text-info me-2"></i>
                            <h6 class="mb-0">Market Outlook</h6>
                        </div>
                        <div class="section-content">
                            ${formatInsightContent(sections.outlook)}
                        </div>
                    </div>
                `;
            }

            // Enhanced Recommendations Section
            if (sections.recommendations) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-star text-primary me-2"></i>
                            <h6 class="mb-0">Enhanced Recommendations</h6>
                        </div>
                        <div class="section-content">
                            ${formatInsightContent(sections.recommendations)}
                        </div>
                    </div>
                `;
            }

            // Key Points Summary
            if (sections.keyPoints && sections.keyPoints.length > 0) {
                html += `
                    <div class="insight-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-key text-secondary me-2"></i>
                            <h6 class="mb-0">Key Takeaways</h6>
                        </div>
                        <div class="section-content">
                            <div class="key-points">
                                ${sections.keyPoints.map(point => `
                                    <div class="key-point">
                                        <i class="fas fa-arrow-right text-primary me-2"></i>
                                        ${point}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }

            html += `</div>`;
            return html;
        }

        // Parse AI insights into structured sections
        function parseAIInsights(text) {
            const sections = {
                validation: '',
                strategic: '',
                risk: '',
                outlook: '',
                recommendations: '',
                keyPoints: []
            };

            // Split text into lines for processing
            const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

            let currentSection = '';
            let currentContent = [];

            for (let line of lines) {
                // Detect section headers
                if (line.includes('Validation') || line.includes('validation')) {
                    if (currentSection && currentContent.length > 0) {
                        sections[currentSection] = currentContent.join(' ');
                    }
                    currentSection = 'validation';
                    currentContent = [];
                } else if (line.includes('Strategic') || line.includes('strategic')) {
                    if (currentSection && currentContent.length > 0) {
                        sections[currentSection] = currentContent.join(' ');
                    }
                    currentSection = 'strategic';
                    currentContent = [];
                } else if (line.includes('Risk') || line.includes('risk')) {
                    if (currentSection && currentContent.length > 0) {
                        sections[currentSection] = currentContent.join(' ');
                    }
                    currentSection = 'risk';
                    currentContent = [];
                } else if (line.includes('Outlook') || line.includes('outlook') || line.includes('Market')) {
                    if (currentSection && currentContent.length > 0) {
                        sections[currentSection] = currentContent.join(' ');
                    }
                    currentSection = 'outlook';
                    currentContent = [];
                } else if (line.includes('Recommendation') || line.includes('recommendation')) {
                    if (currentSection && currentContent.length > 0) {
                        sections[currentSection] = currentContent.join(' ');
                    }
                    currentSection = 'recommendations';
                    currentContent = [];
                } else {
                    // Add content to current section
                    if (line.startsWith('*') || line.startsWith('-') || line.startsWith('•')) {
                        // This is a key point
                        const cleanPoint = line.replace(/^\*+\s*/, '').replace(/^-\s*/, '').replace(/^•\s*/, '');
                        if (cleanPoint.length > 10) {
                            sections.keyPoints.push(cleanPoint);
                        }
                    } else if (currentSection) {
                        currentContent.push(line);
                    } else {
                        // Default to strategic if no section detected
                        if (!currentSection) {
                            currentSection = 'strategic';
                        }
                        currentContent.push(line);
                    }
                }
            }

            // Add the last section
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join(' ');
            }

            return sections;
        }

        // Format insight content with highlighting
        function formatInsightContent(content) {
            if (!content) return '';

            // Highlight important terms
            content = content
                .replace(/\b(bullish|bearish|neutral)\b/gi, '<span class="sentiment-highlight">$1</span>')
                .replace(/\b(PCR|Put-Call Ratio)\b/gi, '<span class="metric-highlight">$1</span>')
                .replace(/\b(Max Pain)\b/gi, '<span class="metric-highlight">$1</span>')
                .replace(/\b(OI|Open Interest)\b/gi, '<span class="metric-highlight">$1</span>')
                .replace(/\b(support|resistance)\b/gi, '<span class="level-highlight">$1</span>')
                .replace(/₹[\d,]+/g, '<span class="price-highlight">$&</span>')
                .replace(/\b\d+%\b/g, '<span class="percentage-highlight">$&</span>');

            return `<p class="insight-text">${content}</p>`;
        }

        // Format AI text content when structured data is not available
        function formatAITextContent(text) {
            if (!text) return 'No analysis content available.';

            // Convert JSON string to formatted display if it's JSON
            try {
                const parsed = JSON.parse(text);
                return formatStructuredAIData(parsed);
            } catch (e) {
                // Not JSON, format as regular text
                return text
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/₹([\d,]+)/g, '<span class="price-highlight">₹$1</span>')
                    .replace(/(\d+)%/g, '<span class="percentage-highlight">$1%</span>');
            }
        }

        // Format structured AI data when JSON is parsed
        function formatStructuredAIData(data) {
            let html = '';

            // Handle different data structures
            if (data.gamma_acceleration_analysis) {
                const gamma = data.gamma_acceleration_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Gamma Acceleration Analysis</h6>
                        <p><strong>Liquidity Score:</strong> ${gamma.liquidity_score}/10</p>
                        <p><strong>Momentum Score:</strong> ${gamma.momentum_score}/10</p>
                        ${gamma.suitable_options ? `<p><strong>Suitable Options:</strong> ${gamma.suitable_options.length} found</p>` : ''}
                    </div>
                `;
            }

            if (data.max_pain_analysis) {
                const maxPain = data.max_pain_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Max Pain Analysis</h6>
                        <p><strong>Max Pain Strike:</strong> ₹${maxPain.max_pain_strike}</p>
                        <p><strong>Bias:</strong> ${maxPain.bias}</p>
                    </div>
                `;
            }

            if (data.pcr_analysis) {
                const pcr = data.pcr_analysis;
                html += `
                    <div class="mb-3">
                        <h6>Put-Call Ratio Analysis</h6>
                        <p><strong>PCR (OI):</strong> ${pcr.pcr_oi?.toFixed(2)}</p>
                        <p><strong>PCR (Volume):</strong> ${pcr.pcr_volume?.toFixed(2)}</p>
                        <p><strong>Bias:</strong> ${pcr.bias}</p>
                    </div>
                `;
            }

            if (data.trade_recommendations && data.trade_recommendations.length > 0) {
                html += `
                    <div class="mb-3">
                        <h6>Trade Recommendations</h6>
                        ${data.trade_recommendations.map(trade => `
                            <div class="trade-rec-item mb-2 p-2 border rounded">
                                <strong>${trade.type} ₹${trade.strike}</strong><br>
                                Entry: ₹${trade.entry_price} | Target: ₹${trade.target} | SL: ₹${trade.stop_loss}
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // If no specific structure found, display as formatted text
            if (!html) {
                html = `<pre class="ai-raw-content">${JSON.stringify(data, null, 2)}</pre>`;
            }

            return html;
        }

        // Format traditional analysis results
        function formatTraditionalAnalysis(tradData) {
            if (!tradData) {
                return '<p>Traditional analysis not available.</p>';
            }

            return `
                <div class="mb-3">
                    <h6>Overall Bias</h6>
                    <p class="h5 text-info">${tradData.overall_bias}</p>
                    <small>Confidence: ${tradData.confidence}%</small>
                </div>
                <div class="mb-3">
                    <h6>Max Pain</h6>
                    <p>Strike: ₹${tradData.max_pain?.max_pain_strike || 'N/A'}</p>
                </div>
                <div class="mb-3">
                    <h6>Put-Call Ratio</h6>
                    <p>PCR (OI): ${tradData.pcr?.pcr_oi?.toFixed(2) || 'N/A'}</p>
                    <p>PCR (Volume): ${tradData.pcr?.pcr_volume?.toFixed(2) || 'N/A'}</p>
                </div>
                <div class="mb-3">
                    <h6>IV Percentile</h6>
                    <p>${tradData.iv_percentile?.iv_percentile?.toFixed(1) || 'N/A'}%</p>
                </div>
            `;
        }

        // Display trade recommendations with live data validation
        function displayTradeRecommendations(aiData) {
            const container = document.getElementById('tradeRecommendations');

            if (!aiData.success || !aiData.trade_recommendations || aiData.trade_recommendations.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No trade recommendations generated.
                        This could be due to:
                        <ul class="mt-2 mb-0">
                            <li>Neutral market conditions</li>
                            <li>Insufficient option liquidity</li>
                            <li>Low confidence signals</li>
                        </ul>
                    </div>
                `;
                return;
            }

            let html = '';

            // Add header with analysis info
            const analysisType = aiData.metadata?.analysis_type || 'unknown';
            const isLiveAnalysis = analysisType.includes('live_calculations');

            if (isLiveAnalysis) {
                html += `
                    <div class="alert alert-success mb-3">
                        <i class="fas fa-check-circle"></i> <strong>Live Trade Recommendations</strong> - Based on real-time option chain analysis
                    </div>
                `;
            }

            aiData.trade_recommendations.forEach((trade, index) => {
                // Skip trades with errors
                if (trade.error) {
                    return;
                }

                const confidenceClass = trade.confidence === 'High' ? 'success' :
                                       trade.confidence === 'Medium' ? 'warning' : 'secondary';

                html += `
                    <div class="trade-recommendation border rounded p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line"></i> Trade ${index + 1}: ${trade.type} Option
                            </h6>
                            <span class="badge bg-${confidenceClass}">${trade.confidence || 'Medium'} Confidence</span>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>Strike:</strong> ₹${trade.strike}
                            </div>
                            <div class="col-md-3">
                                <strong>Entry:</strong> ₹${trade.entry_price}
                            </div>
                            <div class="col-md-3">
                                <strong>Target:</strong> ₹${trade.target}
                            </div>
                            <div class="col-md-3">
                                <strong>Stop Loss:</strong> ₹${trade.stop_loss}
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-6">
                                <strong>Risk-Reward:</strong> ${trade.risk_reward}
                            </div>
                            <div class="col-md-6">
                                <strong>AI Validated:</strong> ${trade.ai_validated ? '✅ Yes' : '⚠️ No'}
                            </div>
                        </div>

                        <div class="mt-2">
                            <strong>Strategy Rationale:</strong>
                            <p class="mb-0 text-muted">${trade.rationale}</p>
                        </div>
                    </div>
                `;
            });

            // Add summary if multiple trades
            if (aiData.trade_recommendations.length > 1) {
                const validTrades = aiData.trade_recommendations.filter(t => !t.error);
                html += `
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb"></i> <strong>Trading Summary:</strong>
                        ${validTrades.length} trade opportunities identified based on current market conditions.
                        Consider position sizing and risk management before execution.
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // Utility functions
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function showResults() {
            document.getElementById('resultsContainer').style.display = 'block';
        }

        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorContainer').style.display = 'none';
        }
    </script>
</body>
</html>
