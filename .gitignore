# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# Flask
instance/
.webassets-cache
flask_session/

# Environment variables and sensitive data
.env
.env.local
.env.new
.env.production
.env.staging
*.env

# Tokens and sensitive data
tokens/
fyers_token.json
*.token
*.key

# Keep watchlists.json but ignore other JSON files with sensitive data
# *.json  # Commented out to allow watchlists.json
config.json
settings.json
secrets.json

# Logs
*.log

# Output files
outputs/
uploads/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db
