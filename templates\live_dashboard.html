<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Market Dashboard - Options Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .signal-card {
            border-left: 4px solid #007bff;
            margin-bottom: 10px;
        }
        .signal-buy { border-left-color: #28a745; }
        .signal-sell { border-left-color: #dc3545; }
        .scanner-result {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
        }
        .live-price {
            font-size: 1.2em;
            font-weight: bold;
        }
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        .metric-card {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 15px;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> Live Market Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/options-analysis">Options Analysis</a>
                <a class="nav-link" href="/analyze-all">Batch Analysis</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-satellite-dish"></i> Live Market Control Panel
                        </h5>
                        <div>
                            <span class="status-indicator" id="connectionStatus"></span>
                            <span id="connectionText">Disconnected</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-success me-2" id="startMonitoring">
                                    <i class="fas fa-play"></i> Start Monitoring
                                </button>
                                <button class="btn btn-danger me-2" id="stopMonitoring">
                                    <i class="fas fa-stop"></i> Stop Monitoring
                                </button>
                                <button class="btn btn-info me-2" id="runScanner">
                                    <i class="fas fa-search"></i> Run Scanner
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="symbolInput" 
                                           placeholder="Add symbols (comma separated)">
                                    <button class="btn btn-primary" id="addSymbols">
                                        <i class="fas fa-plus"></i> Add Symbols
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="totalSymbols">0</div>
                    <div class="metric-label">Monitored Symbols</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="totalSignals">0</div>
                    <div class="metric-label">Trading Signals</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="scannerResults">0</div>
                    <div class="metric-label">Scanner Results</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="lastUpdate">--:--</div>
                    <div class="metric-label">Last Update</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Live Data Panel -->
            <div class="col-md-4">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-broadcast-tower"></i> Live Market Data
                        </h6>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="liveDataContainer">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>Start monitoring to see live data</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pre-Market Scanner -->
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-search"></i> Pre-Market Scanner
                        </h6>
                    </div>
                    <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                        <div id="scannerContainer">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock"></i>
                                <p>Scanner runs 8:50-9:00 AM</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trading Signals -->
            <div class="col-md-4">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-signal"></i> Trading Signals
                        </h6>
                    </div>
                    <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                        <div id="signalsContainer">
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-line"></i>
                                <p>No signals generated yet</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Analysis -->
            <div class="col-md-4">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-area"></i> Technical Analysis
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <select class="form-select" id="chartSymbol">
                                <option value="">Select Symbol for Chart</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="priceChart"></canvas>
                        </div>
                        <div id="indicatorsContainer">
                            <!-- Technical indicators will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class LiveDashboard {
            constructor() {
                this.isMonitoring = false;
                this.updateInterval = null;
                this.chart = null;
                this.initializeEventListeners();
                this.initializeChart();
            }

            initializeEventListeners() {
                document.getElementById('startMonitoring').addEventListener('click', () => this.startMonitoring());
                document.getElementById('stopMonitoring').addEventListener('click', () => this.stopMonitoring());
                document.getElementById('runScanner').addEventListener('click', () => this.runScanner());
                document.getElementById('addSymbols').addEventListener('click', () => this.addSymbols());
                document.getElementById('chartSymbol').addEventListener('change', (e) => this.loadChart(e.target.value));
            }

            initializeChart() {
                const ctx = document.getElementById('priceChart').getContext('2d');
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Price',
                            data: [],
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.1)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false
                            }
                        }
                    }
                });
            }

            async startMonitoring() {
                try {
                    const response = await fetch('/api/live-dashboard/start', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.isMonitoring = true;
                        this.updateConnectionStatus(true);
                        this.startDataUpdates();
                        this.showAlert('Live monitoring started successfully', 'success');
                    } else {
                        this.showAlert(result.message, 'danger');
                    }
                } catch (error) {
                    this.showAlert('Error starting monitoring: ' + error.message, 'danger');
                }
            }

            async stopMonitoring() {
                try {
                    const response = await fetch('/api/live-dashboard/stop', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.isMonitoring = false;
                        this.updateConnectionStatus(false);
                        this.stopDataUpdates();
                        this.showAlert('Live monitoring stopped', 'info');
                    } else {
                        this.showAlert(result.message, 'danger');
                    }
                } catch (error) {
                    this.showAlert('Error stopping monitoring: ' + error.message, 'danger');
                }
            }

            async runScanner() {
                try {
                    const response = await fetch('/api/live-dashboard/scanner', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.displayScannerResults(result.results);
                        this.showAlert(`Scanner found ${result.results.length} gap-up stocks`, 'success');
                    } else {
                        this.showAlert(result.message, 'danger');
                    }
                } catch (error) {
                    this.showAlert('Error running scanner: ' + error.message, 'danger');
                }
            }

            async addSymbols() {
                const input = document.getElementById('symbolInput');
                const symbols = input.value.split(',').map(s => s.trim()).filter(s => s);
                
                if (symbols.length === 0) {
                    this.showAlert('Please enter symbols to add', 'warning');
                    return;
                }

                try {
                    const response = await fetch('/api/live-dashboard/subscribe', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ symbols })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        input.value = '';
                        this.updateSymbolDropdown(symbols);
                        this.showAlert(result.message, 'success');
                    } else {
                        this.showAlert(result.message, 'danger');
                    }
                } catch (error) {
                    this.showAlert('Error adding symbols: ' + error.message, 'danger');
                }
            }

            startDataUpdates() {
                this.updateInterval = setInterval(() => {
                    this.fetchDashboardData();
                }, 2000); // Update every 2 seconds
            }

            stopDataUpdates() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
            }

            async fetchDashboardData() {
                try {
                    const response = await fetch('/api/live-dashboard/data');
                    const result = await response.json();
                    
                    if (result.success) {
                        this.updateDashboard(result.data);
                    }
                } catch (error) {
                    console.error('Error fetching dashboard data:', error);
                }
            }

            updateDashboard(data) {
                // Update metrics
                document.getElementById('totalSymbols').textContent = data.monitored_symbols.length;
                document.getElementById('totalSignals').textContent = data.signals.length;
                document.getElementById('scannerResults').textContent = data.scanner_results.length;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                
                // Update live data
                this.displayLiveData(data.live_data);
                
                // Update signals
                this.displaySignals(data.signals);
                
                // Update connection status
                this.updateConnectionStatus(data.connection_status);
            }

            displayLiveData(liveData) {
                const container = document.getElementById('liveDataContainer');
                
                if (Object.keys(liveData).length === 0) {
                    container.innerHTML = '<div class="text-center text-muted"><p>No live data available</p></div>';
                    return;
                }

                let html = '';
                for (const [symbol, data] of Object.entries(liveData)) {
                    const changeClass = data.change_percent >= 0 ? 'price-up' : 'price-down';
                    const changeIcon = data.change_percent >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                    
                    html += `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <strong>${symbol}</strong><br>
                                <small class="text-muted">Vol: ${data.volume.toLocaleString()}</small>
                            </div>
                            <div class="text-end">
                                <div class="live-price ${changeClass}">₹${data.ltp.toFixed(2)}</div>
                                <small class="${changeClass}">
                                    <i class="fas ${changeIcon}"></i> ${data.change_percent.toFixed(2)}%
                                </small>
                            </div>
                        </div>
                    `;
                }
                
                container.innerHTML = html;
            }

            displaySignals(signals) {
                const container = document.getElementById('signalsContainer');
                
                if (signals.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted"><p>No signals generated yet</p></div>';
                    return;
                }

                let html = '';
                signals.slice(-10).reverse().forEach(signal => {
                    const signalClass = signal.signal_type === 'BUY' ? 'signal-buy' : 'signal-sell';
                    const time = new Date(signal.timestamp).toLocaleTimeString();
                    
                    html += `
                        <div class="card signal-card ${signalClass} mb-2">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between">
                                    <strong>${signal.symbol}</strong>
                                    <span class="badge bg-${signal.signal_type === 'BUY' ? 'success' : 'danger'}">
                                        ${signal.signal_type}
                                    </span>
                                </div>
                                <div class="small">
                                    Price: ₹${signal.price.toFixed(2)} | RSI: ${signal.rsi.toFixed(1)} | 
                                    Confidence: ${signal.confidence}%
                                </div>
                                <div class="small text-muted">${time}</div>
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
            }

            displayScannerResults(results) {
                const container = document.getElementById('scannerContainer');
                
                if (results.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted"><p>No gap-up stocks found</p></div>';
                    return;
                }

                let html = '';
                results.slice(0, 10).forEach(result => {
                    html += `
                        <div class="scanner-result">
                            <div class="d-flex justify-content-between">
                                <strong>${result.symbol}</strong>
                                <span class="badge bg-success">+${result.gap_percent.toFixed(2)}%</span>
                            </div>
                            <div class="small">
                                Current: ₹${result.current_price.toFixed(2)} | 
                                Prev Close: ₹${result.prev_close.toFixed(2)}
                            </div>
                            <div class="small text-muted">
                                Volume: ${result.volume.toLocaleString()}
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
            }

            updateConnectionStatus(connected) {
                const statusIndicator = document.getElementById('connectionStatus');
                const statusText = document.getElementById('connectionText');
                
                if (connected) {
                    statusIndicator.className = 'status-indicator status-connected';
                    statusText.textContent = 'Connected';
                } else {
                    statusIndicator.className = 'status-indicator status-disconnected';
                    statusText.textContent = 'Disconnected';
                }
            }

            updateSymbolDropdown(symbols) {
                const dropdown = document.getElementById('chartSymbol');
                symbols.forEach(symbol => {
                    if (!Array.from(dropdown.options).some(option => option.value === symbol)) {
                        const option = new Option(symbol, symbol);
                        dropdown.add(option);
                    }
                });
            }

            async loadChart(symbol) {
                if (!symbol) return;

                try {
                    const response = await fetch(`/api/live-dashboard/historical/${symbol}?resolution=5&days=1`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const data = result.data;
                        this.chart.data.labels = data.timestamps;
                        this.chart.data.datasets[0].data = data.close;
                        this.chart.data.datasets[0].label = `${symbol} Price`;
                        this.chart.update();
                    }
                } catch (error) {
                    console.error('Error loading chart:', error);
                }
            }

            showAlert(message, type) {
                // Create and show bootstrap alert
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                document.body.appendChild(alertDiv);
                
                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 5000);
            }
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new LiveDashboard();
        });
    </script>
</body>
</html>
