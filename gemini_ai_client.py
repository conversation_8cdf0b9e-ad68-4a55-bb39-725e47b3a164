"""
Gemini 2.0 AI Client for Comprehensive Trading Analysis
Integrates with Google's Gemini 2.0 Flash model for advanced options trading analysis
Handles all trading calculations and analysis using AI instead of traditional methods
"""

import os
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional


class GeminiTradingAnalyzer:
    """
    AI-powered comprehensive trading analysis using Gemini 2.0 Flash model
    Replaces traditional analysis methods with AI-driven calculations and insights
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Gemini AI client

        Args:
            api_key (str, optional): Gemini API key. If not provided, will use environment variable.
        """
        self.api_key = api_key or os.environ.get('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("Gemini API key is required. Set GEMINI_API_KEY environment variable or pass api_key parameter.")

        # Configure Gemini API endpoint
        self.api_endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}"
        self.headers = {
            'Content-Type': 'application/json'
        }

        # Trading strategy parameters
        self.strategy_params = {
            'otm_distance_min': 15,  # 15% minimum OTM distance
            'otm_distance_max': 25,  # 25% maximum OTM distance
            'premium_min': 0.05,     # ₹0.05 minimum premium
            'premium_max': 0.20,     # ₹0.20 maximum premium
            'rsi_oversold': 30,      # RSI oversold level
            'rsi_overbought': 70,    # RSI overbought level
            'adx_trend_strength': 25 # ADX minimum for trend strength
        }

        # AI will handle all calculations directly
        
        # Trading analysis parameters
        self.trading_parameters = {
            "gamma_acceleration": {
                "description": "Focus on options with high gamma potential for rapid price acceleration",
                "selection_criteria": {
                    "beta": "> 1.5",
                    "oi": "> 50k",
                    "otm_distance": "15-25%",
                    "premium_range": "₹0.05-₹0.20"
                }
            },
            "technical_indicators": {
                "rsi": "Relative Strength Index for momentum",
                "adx": "Average Directional Index for trend strength",
                "ema_crossover": "20/50 EMA crossover signals"
            },
            "risk_management": {
                "position_sizing": "Based on volatility and account size",
                "stop_loss": "Technical and time-based stops",
                "profit_booking": "Systematic profit taking levels"
            }
        }
    
    def analyze_option_chain(self, options_data: List[Dict], current_price: float,
                           symbol: str, days_to_expiry: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze option chain data using live calculations + Gemini 2.0 AI model

        Args:
            options_data (List[Dict]): Raw option chain data from Fyers API
            current_price (float): Current price of the underlying
            symbol (str): Symbol being analyzed
            days_to_expiry (int, optional): Days to expiry

        Returns:
            Dict[str, Any]: Comprehensive AI analysis results with live calculations
        """
        try:
            # Let AI handle all calculations directly
            print(f"🤖 Using AI for comprehensive analysis of {symbol}...")
            print(f"📊 Processing {len(options_data)} options with current price ₹{current_price}")

            # Create comprehensive prompt for AI to do all calculations
            prompt = self._create_analysis_prompt(options_data, current_price, symbol, days_to_expiry)
            print(f"📝 Created prompt with {len(prompt)} characters")

            # Get AI analysis
            ai_response = self._get_ai_analysis(prompt)
            print(f"🔄 AI response received: {ai_response.get('success', False)}")

            if ai_response.get('success'):
                # Parse and structure the AI response
                analysis_result = self._parse_ai_response(ai_response.get('response_text', ''))

                # Mark all trade recommendations as AI validated
                if 'trade_recommendations' in analysis_result:
                    for rec in analysis_result['trade_recommendations']:
                        rec['ai_validated'] = True

                # Add metadata
                analysis_result['metadata'] = {
                    'symbol': symbol,
                    'current_price': current_price,
                    'days_to_expiry': days_to_expiry,
                    'analysis_timestamp': datetime.now().isoformat(),
                    'ai_model': 'gemini-2.0-flash',
                    'data_points_analyzed': len(options_data),
                    'analysis_type': 'ai_comprehensive_analysis'
                }

                print(f"✅ AI analysis completed successfully for {symbol}")
                return analysis_result
            else:
                print(f"❌ AI analysis failed: {ai_response.get('error')}")
                return {
                    'success': False,
                    'error': f"AI analysis failed: {ai_response.get('error')}",
                    'basic_info': {
                        'symbol': symbol,
                        'current_price': current_price,
                        'total_options': len(options_data)
                    }
                }

        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
            return {
                'success': False,
                'error': f"Analysis failed: {str(e)}",
                'basic_info': {
                    'symbol': symbol,
                    'current_price': current_price,
                    'total_options': len(options_data)
                }
            }

    def analyze_single_stock(self, options_data: List[Dict], current_price: float,
                           symbol: str, days_to_expiry: Optional[int] = None,
                           technical_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Comprehensive AI-powered single stock analysis with gamma acceleration strategy

        Args:
            options_data (List[Dict]): List of option data dictionaries
            current_price (float): Current price of the underlying
            symbol (str): Symbol being analyzed
            days_to_expiry (Optional[int]): Days to expiry
            technical_data (Optional[Dict]): Technical indicators (RSI, ADX, etc.)

        Returns:
            Dict[str, Any]: Comprehensive AI analysis results with trade recommendations
        """
        try:
            print(f"🤖 AI analyzing single stock: {symbol}")
            print(f"📊 Processing {len(options_data)} options with current price ₹{current_price}")

            # Create comprehensive prompt for single stock analysis
            prompt = self._create_single_stock_prompt(options_data, current_price, symbol,
                                                    days_to_expiry, technical_data)

            # Get AI analysis
            ai_response = self._get_ai_analysis(prompt)

            if ai_response.get('success'):
                analysis_result = self._parse_ai_response(ai_response.get('response_text', ''))

                # Add gamma acceleration strategy validation
                analysis_result = self._apply_gamma_strategy_filter(analysis_result, current_price)

                # Add metadata
                analysis_result['metadata'] = {
                    'symbol': symbol,
                    'current_price': current_price,
                    'days_to_expiry': days_to_expiry,
                    'analysis_timestamp': datetime.now().isoformat(),
                    'ai_model': 'gemini-2.0-flash',
                    'strategy': 'gamma_acceleration',
                    'analysis_type': 'ai_single_stock_analysis'
                }

                return analysis_result
            else:
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI analysis failed'),
                    'symbol': symbol
                }

        except Exception as e:
            print(f"Error in AI single stock analysis: {str(e)}")
            return {
                'success': False,
                'error': f"Analysis error: {str(e)}",
                'symbol': symbol
            }

    def analyze_multiple_stocks(self, stocks_data: List[Dict]) -> Dict[str, Any]:
        """
        AI-powered analysis of multiple stocks to find best opportunities

        Args:
            stocks_data (List[Dict]): List of stock data with options chains
                Each dict should contain: symbol, current_price, options_data, technical_data

        Returns:
            Dict[str, Any]: Ranked list of best trading opportunities
        """
        try:
            print(f"🤖 AI analyzing {len(stocks_data)} stocks for best opportunities")

            # Create batch analysis prompt
            prompt = self._create_batch_analysis_prompt(stocks_data)

            # Get AI analysis
            ai_response = self._get_ai_analysis(prompt)

            if ai_response.get('success'):
                analysis_result = self._parse_ai_response(ai_response.get('response_text', ''))

                # Apply gamma acceleration strategy to all recommendations
                if 'ranked_opportunities' in analysis_result:
                    for opportunity in analysis_result['ranked_opportunities']:
                        opportunity = self._apply_gamma_strategy_filter(opportunity,
                                                                     opportunity.get('current_price', 0))

                # Add metadata
                analysis_result['metadata'] = {
                    'stocks_analyzed': len(stocks_data),
                    'analysis_timestamp': datetime.now().isoformat(),
                    'ai_model': 'gemini-2.0-flash',
                    'strategy': 'gamma_acceleration',
                    'analysis_type': 'ai_batch_analysis'
                }

                return analysis_result
            else:
                return {
                    'success': False,
                    'error': ai_response.get('error', 'Batch AI analysis failed')
                }

        except Exception as e:
            print(f"Error in AI batch analysis: {str(e)}")
            return {
                'success': False,
                'error': f"Batch analysis error: {str(e)}"
            }

    def _create_analysis_prompt(self, options_data: List[Dict], current_price: float,
                              symbol: str, days_to_expiry: Optional[int]) -> str:
        """
        Create a comprehensive analysis prompt for Gemini 2.0
        """
        # Prepare option chain data summary
        data_summary = self._summarize_option_data(options_data, current_price)
        
        prompt = f"""
You are an expert options trader and quantitative analyst. Analyze the following real-time option chain data for {symbol} and provide comprehensive trading insights.

**CURRENT MARKET DATA:**
- Symbol: {symbol}
- Current Price: ₹{current_price}
- Days to Expiry: {days_to_expiry or 'Not specified'}
- Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**OPTION CHAIN DATA SUMMARY:**
{data_summary}

**CRITICAL CALCULATION REQUIREMENTS:**
You must calculate these metrics using the exact option data provided:

1. **MAX PAIN CALCULATION:**
   - For each strike price, calculate total pain = sum of (ITM call losses + ITM put losses)
   - Max pain strike = strike with MINIMUM total pain
   - Use actual OI values from the data
   - Return the exact strike price as a number

2. **PUT-CALL RATIO (PCR) CALCULATION:**
   - PCR (OI) = Total Put OI ÷ Total Call OI
   - PCR (Volume) = Total Put Volume ÷ Total Call Volume
   - Use exact numbers from the option chain data
   - Return precise decimal values (e.g., 1.234, not "approximately 1.2")

3. **MARKET SENTIMENT ANALYSIS:**
   - Based on PCR: <0.7 = Bullish, >1.3 = Bearish, 0.7-1.3 = Neutral
   - Based on Max Pain vs Current Price difference
   - Confidence level as percentage (0-100)

4. **TRADE RECOMMENDATIONS:**
   - Use ONLY strikes from the "ALL AVAILABLE CALL/PUT STRIKES" sections above
   - Entry price = exact LTP value shown for that strike
   - Target = 1.5x to 2x entry price
   - Stop loss = 0.6x to 0.8x entry price
   - Focus on strikes with OI > 1000 and reasonable volume

**CRITICAL REQUIREMENTS - READ CAREFULLY:**
- ONLY use strike prices listed in the "ALL AVAILABLE CALL/PUT STRIKES" sections above
- DO NOT invent strikes like 1580, 1620 - these don't exist in the data
- Entry price MUST be the exact LTP value shown for that specific strike
- Example: If data shows "Strike ₹720: OI=5,000, Vol=200, LTP=₹15.50", then use Strike=720, Entry=15.50
- Double-check every recommended strike exists in the provided option chain data
- Return exact numerical values from the data, not approximations

**OUTPUT FORMAT:**
Please structure your response as a JSON object with the following format:
{{
    "success": true,
    "max_pain_analysis": {{
        "max_pain_strike": number,
        "current_vs_max_pain": "analysis text",
        "directional_bias": "Bullish/Bearish/Neutral"
    }},
    "pcr_analysis": {{
        "pcr_oi": number,
        "pcr_volume": number,
        "interpretation": "analysis text",
        "bias": "Bullish/Bearish/Neutral"
    }},
    "oi_analysis": {{
        "significant_changes": [],
        "institutional_activity": "analysis text",
        "key_levels": []
    }},
    "gamma_analysis": {{
        "gamma_exposure_levels": [],
        "squeeze_potential": "analysis text",
        "dealer_positioning": "analysis text"
    }},
    "iv_analysis": {{
        "current_iv_percentile": number,
        "iv_skew": "analysis text",
        "volatility_regime": "High/Medium/Low"
    }},
    "trade_recommendations": [
        {{
            "type": "Call/Put",
            "strike": number,
            "entry_price": number,
            "stop_loss": number,
            "target": number,
            "risk_reward": number,
            "rationale": "detailed explanation"
        }}
    ],
    "overall_bias": "Bullish/Bearish/Neutral",
    "confidence_level": number,
    "key_insights": []
}}

Ensure all numerical values are realistic and based on the actual data provided. Focus on actionable insights for gamma acceleration trading strategy.
"""
        
        return prompt

    def _get_ai_analysis(self, prompt: str) -> Dict[str, Any]:
        """Get comprehensive AI analysis"""
        try:
            if not self.api_key:
                return {
                    'success': False,
                    'error': 'Gemini API key not available'
                }

            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 4000,
                    "topP": 0.8,
                    "topK": 10
                }
            }

            response = requests.post(self.api_endpoint, headers=self.headers, json=payload, timeout=60)

            if response.status_code == 200:
                response_data = response.json()
                ai_text = response_data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
                return {
                    'success': True,
                    'response_text': ai_text,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': f"Gemini API error: {response.status_code} - {response.text}"
                }

        except Exception as e:
            return {
                'success': False,
                'error': f"AI request failed: {str(e)}"
            }

    def _summarize_option_data(self, options_data: List[Dict], current_price: float) -> str:
        """
        Create a concise summary of option chain data for the AI prompt
        """
        if not options_data:
            return "No option data available"
        
        # Group by option type and calculate key metrics
        calls = [opt for opt in options_data if opt.get('Option Type') == 'CE']
        puts = [opt for opt in options_data if opt.get('Option Type') == 'PE']
        
        # Calculate total OI and volume
        total_call_oi = sum(opt.get('OI', 0) for opt in calls)
        total_put_oi = sum(opt.get('OI', 0) for opt in puts)
        total_call_volume = sum(opt.get('Volume', 0) for opt in calls)
        total_put_volume = sum(opt.get('Volume', 0) for opt in puts)
        
        # Find ATM and key strikes
        strikes = sorted(set(opt.get('Strike Price', 0) for opt in options_data if opt.get('Strike Price', 0) > 0))
        atm_strike = min(strikes, key=lambda x: abs(x - current_price)) if strikes else current_price

        print(f"📈 Available strikes: {strikes[:10]}..." if len(strikes) > 10 else f"📈 Available strikes: {strikes}")
        print(f"🎯 ATM Strike: ₹{atm_strike}, Current Price: ₹{current_price}")
        
        # Get top 10 strikes by OI for calls and puts (more data for AI)
        top_call_strikes = sorted(calls, key=lambda x: x.get('OI', 0), reverse=True)[:10]
        top_put_strikes = sorted(puts, key=lambda x: x.get('OI', 0), reverse=True)[:10]

        # Get all strikes with significant OI (>100) for AI to choose from
        significant_calls = [opt for opt in calls if opt.get('OI', 0) > 100]
        significant_puts = [opt for opt in puts if opt.get('OI', 0) > 100]
        
        summary = f"""
**OPTION CHAIN SUMMARY:**
- Total Options: {len(options_data)}
- Strike Range: {min(strikes) if strikes else 'N/A'} to {max(strikes) if strikes else 'N/A'}
- ATM Strike: {atm_strike}

**OPEN INTEREST:**
- Total Call OI: {total_call_oi:,}
- Total Put OI: {total_put_oi:,}
- PCR (OI): {total_put_oi/total_call_oi if total_call_oi > 0 else 'N/A'}

**VOLUME:**
- Total Call Volume: {total_call_volume:,}
- Total Put Volume: {total_put_volume:,}
- PCR (Volume): {total_put_volume/total_call_volume if total_call_volume > 0 else 'N/A'}

**TOP CALL STRIKES BY OI:**
{self._format_top_strikes(top_call_strikes)}

**TOP PUT STRIKES BY OI:**
{self._format_top_strikes(top_put_strikes)}

**ALL AVAILABLE CALL STRIKES (for trade recommendations):**
{self._format_all_strikes(significant_calls)}

**ALL AVAILABLE PUT STRIKES (for trade recommendations):**
{self._format_all_strikes(significant_puts)}
"""
        
        return summary
    
    def _format_top_strikes(self, strikes: List[Dict]) -> str:
        """Format top strikes for display"""
        if not strikes:
            return "No data available"

        formatted = []
        for strike in strikes:
            formatted.append(f"  {strike.get('Strike Price', 0)}: OI={strike.get('OI', 0):,}, Vol={strike.get('Volume', 0):,}, LTP=₹{strike.get('LTP', 0)}")

        return "\n".join(formatted)

    def _format_all_strikes(self, strikes: List[Dict]) -> str:
        """Format all available strikes for AI to choose from"""
        if not strikes:
            return "No strikes with significant OI available"

        # Sort by strike price for easy reading
        sorted_strikes = sorted(strikes, key=lambda x: x.get('Strike Price', 0))

        formatted = []
        for strike in sorted_strikes:
            strike_price = strike.get('Strike Price', 0)
            oi = strike.get('OI', 0)
            volume = strike.get('Volume', 0)
            ltp = strike.get('LTP', 0)
            formatted.append(f"  Strike ₹{strike_price}: OI={oi:,}, Vol={volume:,}, LTP=₹{ltp}")

        return "\n".join(formatted)
    
    def _parse_ai_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse and validate AI response
        """
        try:
            # Try to extract JSON from the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                parsed_response = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['success', 'max_pain_analysis', 'pcr_analysis', 'trade_recommendations']
                for field in required_fields:
                    if field not in parsed_response:
                        parsed_response[field] = self._get_default_field_value(field)
                
                return parsed_response
            else:
                # If no JSON found, create structured response from text
                return self._create_structured_response_from_text(response_text)
                
        except json.JSONDecodeError:
            # Fallback: create structured response from text
            return self._create_structured_response_from_text(response_text)
    
    def _create_structured_response_from_text(self, text: str) -> Dict[str, Any]:
        """
        Create structured response when JSON parsing fails
        """
        return {
            'success': True,
            'ai_analysis_text': text,
            'max_pain_analysis': {'analysis': 'See AI analysis text'},
            'pcr_analysis': {'analysis': 'See AI analysis text'},
            'oi_analysis': {'analysis': 'See AI analysis text'},
            'gamma_analysis': {'analysis': 'See AI analysis text'},
            'iv_analysis': {'analysis': 'See AI analysis text'},
            'trade_recommendations': [],
            'overall_bias': 'Neutral',
            'confidence_level': 50,
            'key_insights': ['See detailed AI analysis text above']
        }
    
    def _get_default_field_value(self, field: str) -> Any:
        """Get default value for missing fields"""
        defaults = {
            'success': True,
            'max_pain_analysis': {'analysis': 'Analysis not available'},
            'pcr_analysis': {'analysis': 'Analysis not available'},
            'oi_analysis': {'analysis': 'Analysis not available'},
            'gamma_analysis': {'analysis': 'Analysis not available'},
            'iv_analysis': {'analysis': 'Analysis not available'},
            'trade_recommendations': [],
            'overall_bias': 'Neutral',
            'confidence_level': 50
        }
        return defaults.get(field, 'Not available')

    def _create_single_stock_prompt(self, options_data: List[Dict], current_price: float,
                                  symbol: str, days_to_expiry: Optional[int],
                                  technical_data: Optional[Dict]) -> str:
        """Create prompt for single stock analysis with gamma acceleration strategy"""
        data_summary = self._summarize_option_data(options_data, current_price)

        technical_info = ""
        if technical_data:
            technical_info = f"""
**TECHNICAL INDICATORS:**
- RSI: {technical_data.get('rsi', 'N/A')}
- ADX: {technical_data.get('adx', 'N/A')}
- EMA 20: {technical_data.get('ema_20', 'N/A')}
- EMA 50: {technical_data.get('ema_50', 'N/A')}
- Volume Trend: {technical_data.get('volume_trend', 'N/A')}
"""

        prompt = f"""
You are an expert options trader specializing in gamma acceleration strategies. Analyze {symbol} for high-probability options trades.

**GAMMA ACCELERATION STRATEGY PARAMETERS:**
- Target OTM Distance: {self.strategy_params['otm_distance_min']}-{self.strategy_params['otm_distance_max']}%
- Premium Range: ₹{self.strategy_params['premium_min']}-₹{self.strategy_params['premium_max']}
- RSI Levels: Oversold <{self.strategy_params['rsi_oversold']}, Overbought >{self.strategy_params['rsi_overbought']}
- ADX Trend Strength: >{self.strategy_params['adx_trend_strength']}

**CURRENT MARKET DATA:**
- Symbol: {symbol}
- Current Price: ₹{current_price}
- Days to Expiry: {days_to_expiry or 'Not specified'}
- Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{technical_info}

{data_summary}

**ANALYSIS REQUIREMENTS:**
1. Calculate exact Max Pain, PCR, and Gamma Exposure
2. Identify options matching gamma acceleration criteria
3. Focus on high-volume, liquid options with strong momentum potential
4. Provide specific entry, target, and stop-loss levels
5. Consider technical indicators for timing

**OUTPUT FORMAT (JSON):**
{{
    "success": true,
    "gamma_acceleration_analysis": {{
        "suitable_options": [],
        "momentum_score": number,
        "liquidity_score": number
    }},
    "max_pain_analysis": {{"max_pain_strike": number, "bias": "Bullish/Bearish/Neutral"}},
    "pcr_analysis": {{"pcr_oi": number, "pcr_volume": number, "bias": "Bullish/Bearish/Neutral"}},
    "technical_analysis": {{
        "rsi_signal": "Bullish/Bearish/Neutral",
        "adx_strength": "Strong/Weak",
        "trend_direction": "Up/Down/Sideways"
    }},
    "trade_recommendations": [
        {{
            "type": "Call/Put",
            "strike": number,
            "entry_price": number,
            "target": number,
            "stop_loss": number,
            "gamma_score": number,
            "rationale": "detailed explanation"
        }}
    ],
    "overall_bias": "Bullish/Bearish/Neutral",
    "confidence_level": number,
    "risk_assessment": "Low/Medium/High"
}}
"""
        return prompt

    def _create_batch_analysis_prompt(self, stocks_data: List[Dict]) -> str:
        """Create prompt for batch analysis of multiple stocks"""
        stocks_summary = []
        for stock in stocks_data:
            symbol = stock.get('symbol', 'Unknown')
            current_price = stock.get('current_price', 0)
            options_count = len(stock.get('options_data', []))

            stocks_summary.append(f"- {symbol}: Price ₹{current_price}, {options_count} options")

        prompt = f"""
You are an expert options trader analyzing {len(stocks_data)} stocks to find the best gamma acceleration trading opportunities.

**STOCKS TO ANALYZE:**
{chr(10).join(stocks_summary)}

**GAMMA ACCELERATION STRATEGY:**
- Target OTM Distance: {self.strategy_params['otm_distance_min']}-{self.strategy_params['otm_distance_max']}%
- Premium Range: ₹{self.strategy_params['premium_min']}-₹{self.strategy_params['premium_max']}
- Focus on high gamma, high volume options
- Prioritize momentum and liquidity

**ANALYSIS REQUIREMENTS:**
1. Rank all stocks by trading opportunity quality
2. Consider liquidity, momentum, and technical setup
3. Provide top 5 trade recommendations across all stocks
4. Include risk assessment for each opportunity

**OUTPUT FORMAT (JSON):**
{{
    "success": true,
    "ranked_opportunities": [
        {{
            "rank": number,
            "symbol": "string",
            "current_price": number,
            "opportunity_score": number,
            "trade_type": "Call/Put",
            "strike": number,
            "entry_price": number,
            "target": number,
            "stop_loss": number,
            "rationale": "detailed explanation",
            "risk_level": "Low/Medium/High"
        }}
    ],
    "market_overview": {{
        "bullish_stocks": number,
        "bearish_stocks": number,
        "neutral_stocks": number,
        "overall_market_bias": "Bullish/Bearish/Neutral"
    }},
    "top_picks": [],
    "analysis_summary": "Overall market analysis and recommendations"
}}
"""
        return prompt

    def _apply_gamma_strategy_filter(self, analysis_result: Dict, current_price: float) -> Dict:
        """Apply gamma acceleration strategy filters to analysis results"""
        if not analysis_result.get('success'):
            return analysis_result

        # Filter trade recommendations based on gamma strategy criteria
        if 'trade_recommendations' in analysis_result:
            filtered_recommendations = []

            for rec in analysis_result['trade_recommendations']:
                strike = rec.get('strike', 0)
                entry_price = rec.get('entry_price', 0)

                # Calculate OTM distance
                if rec.get('type') == 'Call':
                    otm_distance = ((strike - current_price) / current_price) * 100
                else:  # Put
                    otm_distance = ((current_price - strike) / current_price) * 100

                # Apply gamma strategy filters
                if (self.strategy_params['otm_distance_min'] <= otm_distance <= self.strategy_params['otm_distance_max'] and
                    self.strategy_params['premium_min'] <= entry_price <= self.strategy_params['premium_max']):

                    rec['gamma_strategy_compliant'] = True
                    rec['otm_distance_pct'] = round(otm_distance, 2)
                    filtered_recommendations.append(rec)
                else:
                    rec['gamma_strategy_compliant'] = False
                    rec['otm_distance_pct'] = round(otm_distance, 2)
                    rec['filter_reason'] = f"OTM: {otm_distance:.1f}%, Premium: ₹{entry_price}"

            # Keep all recommendations but mark compliance
            analysis_result['trade_recommendations'] = analysis_result['trade_recommendations']
            analysis_result['gamma_compliant_trades'] = filtered_recommendations

        return analysis_result

    # Removed unused methods - AI handles everything directly
