import numpy as np
import pandas as pd
from datetime import datetime
import math
from scipy.stats import norm

class OptionsAnalysis:
    """
    Class for performing advanced options analysis techniques
    """

    def __init__(self):
        """Initialize the OptionsAnalysis class"""
        # Default parameters for Black-Scholes calculations
        self.risk_free_rate = 0.05  # 5% risk-free rate
        self.days_to_expiry = 30    # Default 30 days to expiry

    def calculate_max_pain(self, options_data, current_price, strike_step=50):
        """
        Calculate the Max Pain point - the strike price where option writers face the least financial pain

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            strike_step (int, optional): Step size between strikes for interpolation. Defaults to 50.

        Returns:
            dict: Max Pain analysis results
        """
        if not options_data or len(options_data) == 0:
            return {
                "max_pain_strike": current_price,
                "pain_by_strike": [],
                "current_price": current_price,
                "analysis": "No data available for Max Pain calculation"
            }

        # Group options by strike price and option type
        strikes = {}
        for option in options_data:
            strike = option.get('Strike Price', 0)
            option_type = option.get('Option Type', '')
            oi = option.get('OI', 0)

            if strike not in strikes:
                strikes[strike] = {'CE': 0, 'PE': 0}

            if option_type in ['CE', 'PE']:
                strikes[strike][option_type] = oi

        # Calculate pain at each strike
        pain_by_strike = []
        for strike, oi_data in strikes.items():
            call_oi = oi_data['CE']
            put_oi = oi_data['PE']

            # Calculate total pain at this strike
            total_pain = 0

            # Pain for call writers if price > strike (calls are ITM)
            for s, s_data in strikes.items():
                if s < strike:
                    # Call writers lose (strike - s) per contract
                    total_pain += s_data['CE'] * (strike - s)

                if s > strike:
                    # Put writers lose (s - strike) per contract
                    total_pain += s_data['PE'] * (s - strike)

            pain_by_strike.append({
                'strike': strike,
                'pain': total_pain,
                'call_oi': call_oi,
                'put_oi': put_oi
            })

        # Sort by strike price
        pain_by_strike.sort(key=lambda x: x['strike'])

        # Find the strike with minimum pain
        if pain_by_strike:
            min_pain_point = min(pain_by_strike, key=lambda x: x['pain'])
            max_pain_strike = min_pain_point['strike']
        else:
            max_pain_strike = current_price

        # Generate analysis text
        if max_pain_strike > current_price:
            analysis = f"Max Pain ({max_pain_strike}) is above current price ({current_price}), suggesting potential upward pressure"
        elif max_pain_strike < current_price:
            analysis = f"Max Pain ({max_pain_strike}) is below current price ({current_price}), suggesting potential downward pressure"
        else:
            analysis = f"Max Pain ({max_pain_strike}) is at current price ({current_price}), suggesting consolidation"

        return {
            "max_pain_strike": max_pain_strike,
            "pain_by_strike": pain_by_strike,
            "current_price": current_price,
            "analysis": analysis
        }

    def calculate_put_call_ratio(self, options_data):
        """
        Calculate the Put-Call Ratio based on open interest

        Args:
            options_data (list): List of option data dictionaries

        Returns:
            dict: PCR analysis results
        """
        if not options_data or len(options_data) == 0:
            return {
                "pcr": 0,
                "total_call_oi": 0,
                "total_put_oi": 0,
                "analysis": "No data available for PCR calculation"
            }

        # Calculate total OI for calls and puts
        total_call_oi = 0
        total_put_oi = 0

        for option in options_data:
            option_type = option.get('Option Type', '')
            oi = option.get('OI', 0)

            if option_type == 'CE':
                total_call_oi += oi
            elif option_type == 'PE':
                total_put_oi += oi

        # Calculate PCR
        pcr = total_put_oi / total_call_oi if total_call_oi > 0 else 0

        # Generate analysis text
        if pcr > 1.3:
            analysis = f"High PCR ({pcr:.2f}) suggests excessive bearishness, potential bullish reversal"
        elif pcr < 0.7:
            analysis = f"Low PCR ({pcr:.2f}) suggests excessive bullishness, potential bearish reversal"
        else:
            analysis = f"Mid-range PCR ({pcr:.2f}) suggests balanced sentiment"

        return {
            "pcr": pcr,
            "total_call_oi": total_call_oi,
            "total_put_oi": total_put_oi,
            "analysis": analysis
        }

    def calculate_oi_change_analysis(self, options_data):
        """
        Analyze open interest changes to identify accumulation or distribution patterns

        Args:
            options_data (list): List of option data dictionaries

        Returns:
            dict: OI change analysis results
        """
        if not options_data or len(options_data) == 0:
            return {
                "call_oi_change": 0,
                "put_oi_change": 0,
                "net_oi_change": 0,
                "analysis": "No data available for OI change analysis"
            }

        # Calculate total OI change for calls and puts
        call_oi_change = 0
        put_oi_change = 0

        for option in options_data:
            option_type = option.get('Option Type', '')
            oi_change = option.get('Change in OI', 0)

            if option_type == 'CE':
                call_oi_change += oi_change
            elif option_type == 'PE':
                put_oi_change += oi_change

        # Calculate net OI change
        net_oi_change = call_oi_change - put_oi_change

        # Generate analysis text
        if call_oi_change > 0 and put_oi_change < 0:
            analysis = f"Call OI increasing ({call_oi_change:,}) + Put OI decreasing ({put_oi_change:,}) = Bullish"
        elif put_oi_change > 0 and call_oi_change < 0:
            analysis = f"Put OI increasing ({put_oi_change:,}) + Call OI decreasing ({call_oi_change:,}) = Bearish"
        elif call_oi_change > 0 and put_oi_change > 0:
            if call_oi_change > put_oi_change:
                analysis = f"Both OI increasing, but Call OI change ({call_oi_change:,}) > Put OI change ({put_oi_change:,}) = Moderately Bullish"
            elif put_oi_change > call_oi_change:
                analysis = f"Both OI increasing, but Put OI change ({put_oi_change:,}) > Call OI change ({call_oi_change:,}) = Moderately Bearish"
            else:
                analysis = f"Both OI increasing equally = Neutral"
        elif call_oi_change < 0 and put_oi_change < 0:
            if call_oi_change > put_oi_change:  # Less negative
                analysis = f"Both OI decreasing, but Call OI decrease ({call_oi_change:,}) < Put OI decrease ({put_oi_change:,}) = Moderately Bullish"
            elif put_oi_change > call_oi_change:  # Less negative
                analysis = f"Both OI decreasing, but Put OI decrease ({put_oi_change:,}) < Call OI decrease ({call_oi_change:,}) = Moderately Bearish"
            else:
                analysis = f"Both OI decreasing equally = Neutral"
        else:
            analysis = f"No significant OI changes = Neutral"

        return {
            "call_oi_change": call_oi_change,
            "put_oi_change": put_oi_change,
            "net_oi_change": net_oi_change,
            "analysis": analysis
        }

    def black_scholes_d1(self, S, K, T, r, sigma):
        """
        Calculate d1 in the Black-Scholes formula

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset

        Returns:
            float: d1 value
        """
        if sigma <= 0 or T <= 0:
            return 0
        return (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))

    def black_scholes_d2(self, S, K, T, r, sigma):
        """
        Calculate d2 in the Black-Scholes formula

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset

        Returns:
            float: d2 value
        """
        if sigma <= 0 or T <= 0:
            return 0
        return self.black_scholes_d1(S, K, T, r, sigma) - sigma * np.sqrt(T)

    def calculate_option_price(self, S, K, T, r, sigma, option_type):
        """
        Calculate option price using Black-Scholes model

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset
            option_type (str): 'call' or 'put'

        Returns:
            float: Option price
        """
        if sigma <= 0 or T <= 0:
            # For zero volatility or expired options
            if option_type.lower() == 'call':
                return max(0, S - K)
            else:
                return max(0, K - S)

        d1 = self.black_scholes_d1(S, K, T, r, sigma)
        d2 = self.black_scholes_d2(S, K, T, r, sigma)

        if option_type.lower() == 'call':
            return S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        else:
            return K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

    def calculate_delta(self, S, K, T, r, sigma, option_type):
        """
        Calculate option delta

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset
            option_type (str): 'call' or 'put'

        Returns:
            float: Option delta
        """
        if sigma <= 0 or T <= 0:
            # For zero volatility or expired options
            if option_type.lower() == 'call':
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0

        d1 = self.black_scholes_d1(S, K, T, r, sigma)

        if option_type.lower() == 'call':
            return norm.cdf(d1)
        else:
            return norm.cdf(d1) - 1

    def calculate_gamma(self, S, K, T, r, sigma):
        """
        Calculate option gamma (same for calls and puts)

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset

        Returns:
            float: Option gamma
        """
        if sigma <= 0 or T <= 0:
            return 0

        d1 = self.black_scholes_d1(S, K, T, r, sigma)
        return norm.pdf(d1) / (S * sigma * np.sqrt(T))

    def calculate_vega(self, S, K, T, r, sigma):
        """
        Calculate option vega (same for calls and puts)

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset

        Returns:
            float: Option vega (as a percentage)
        """
        if T <= 0:
            return 0

        d1 = self.black_scholes_d1(S, K, T, r, sigma)
        return S * np.sqrt(T) * norm.pdf(d1) / 100  # Divide by 100 to get percentage

    def calculate_theta(self, S, K, T, r, sigma, option_type):
        """
        Calculate option theta (time decay)

        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            r (float): Risk-free interest rate
            sigma (float): Volatility of the underlying asset
            option_type (str): 'call' or 'put'

        Returns:
            float: Option theta (per day)
        """
        if sigma <= 0 or T <= 0:
            return 0

        d1 = self.black_scholes_d1(S, K, T, r, sigma)
        d2 = self.black_scholes_d2(S, K, T, r, sigma)

        common_term = -(S * sigma * norm.pdf(d1)) / (2 * np.sqrt(T))

        if option_type.lower() == 'call':
            theta = common_term - r * K * np.exp(-r * T) * norm.cdf(d2)
        else:
            theta = common_term + r * K * np.exp(-r * T) * norm.cdf(-d2)

        return theta / 365  # Convert to daily theta

    def calculate_gamma_exposure(self, options_data, current_price, days_to_expiry=None):
        """
        Calculate Gamma Exposure (GEX) for the option chain

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            days_to_expiry (int, optional): Days to expiration. Defaults to None.

        Returns:
            dict: GEX analysis results
        """
        if not options_data or len(options_data) == 0:
            return {
                "net_gex": 0,
                "gex_by_strike": [],
                "current_price": current_price,
                "analysis": "No data available for GEX calculation"
            }

        # Use provided days to expiry or default, ensuring it's a number
        try:
            if days_to_expiry is not None:
                days_to_expiry_int = int(days_to_expiry)
                T = days_to_expiry_int / 365
            else:
                T = self.days_to_expiry / 365
        except (ValueError, TypeError):
            # If conversion fails, use the default
            T = self.days_to_expiry / 365

        # Calculate GEX for each option
        gex_by_strike = []
        total_call_gamma = 0
        total_put_gamma = 0

        for option in options_data:
            strike = option.get('Strike Price', 0)
            option_type = option.get('Option Type', '')
            oi = option.get('OI', 0)

            # Skip if no OI or invalid option type
            if oi <= 0 or option_type not in ['CE', 'PE']:
                continue

            # Use IV from data or default to 30%
            iv = option.get('IV', 30) / 100  # Convert from percentage to decimal
            if iv <= 0:
                iv = 0.3  # Default to 30% if IV is missing or invalid

            # Calculate gamma
            gamma = self.calculate_gamma(current_price, strike, T, self.risk_free_rate, iv)

            # Calculate GEX contribution (gamma * OI * spot price * 100)
            # Multiply by 100 because each option contract typically represents 100 shares
            gex = gamma * oi * current_price * 100

            # For puts, GEX is negative
            if option_type == 'PE':
                gex = -gex
                total_put_gamma += gex
            else:
                total_call_gamma += gex

            gex_by_strike.append({
                'strike': strike,
                'option_type': option_type,
                'gamma': gamma,
                'oi': oi,
                'gex': gex
            })

        # Calculate net GEX
        net_gex = total_call_gamma + total_put_gamma

        # Generate analysis text
        if net_gex > 1000000:  # More than 1M positive GEX
            analysis = f"Strong positive GEX ({net_gex:,.0f}) suggests upward price stability (resistance to downward moves)"
        elif net_gex < -1000000:  # More than 1M negative GEX
            analysis = f"Strong negative GEX ({net_gex:,.0f}) suggests downward price instability (vulnerability to crashes)"
        elif net_gex > 0:
            analysis = f"Mild positive GEX ({net_gex:,.0f}) suggests slight upward price stability"
        elif net_gex < 0:
            analysis = f"Mild negative GEX ({net_gex:,.0f}) suggests slight downward price instability"
        else:
            analysis = f"Balanced GEX ({net_gex:,.0f}) suggests low directional pressure"

        # Sort by strike price
        gex_by_strike.sort(key=lambda x: x['strike'])

        # Aggregate GEX by strike price
        aggregated_gex = {}
        for item in gex_by_strike:
            strike = item['strike']
            if strike not in aggregated_gex:
                aggregated_gex[strike] = 0
            aggregated_gex[strike] += item['gex']

        # Convert to list format
        gex_by_strike_agg = [{'strike': k, 'gex': v} for k, v in aggregated_gex.items()]
        gex_by_strike_agg.sort(key=lambda x: x['strike'])

        return {
            "net_gex": net_gex,
            "call_gex": total_call_gamma,
            "put_gex": total_put_gamma,
            "gex_by_strike": gex_by_strike_agg,
            "current_price": current_price,
            "analysis": analysis
        }

    def calculate_iv_percentile(self, options_data, historical_iv=None):
        """
        Calculate IV Percentile for the option chain with improved Fyers API support

        Args:
            options_data (list): List of option data dictionaries
            historical_iv (list, optional): List of historical IV values. Defaults to None.

        Returns:
            dict: IV Percentile analysis results
        """
        print("\n--- Calculating IV Percentile ---")

        if not options_data or len(options_data) == 0:
            print("No options data available")
            return {
                "current_iv": 30.0,  # Default IV
                "iv_percentile": 50.0,
                "analysis": "No data available for IV Percentile calculation, using default values"
            }

        # Debug: Print first option to see structure
        print(f"First option data sample: {options_data[0]}")

        # Check if we have Fyers API data with iv field (lowercase)
        has_fyers_iv = False
        for opt in options_data[:5]:  # Check first few options
            if 'iv' in opt:
                print(f"Found Fyers API IV field (lowercase): {opt.get('iv', 0)}")
                has_fyers_iv = True
                break

        # Calculate average IV from ATM options
        # First, find the ATM strike (closest to current price)
        # Handle both 'Strike Price' and 'strikePrice' formats (Fyers API uses lowercase)
        strikes = set()
        for opt in options_data:
            strike = opt.get('Strike Price', 0)
            if strike == 0:
                strike = opt.get('strikePrice', 0)
            if strike > 0:
                strikes.add(strike)

        if not strikes:
            print("No strike price data available")
            return {
                "current_iv": 30.0,  # Default IV
                "iv_percentile": 50.0,
                "analysis": "No strike price data available, using default IV"
            }

        # Get the current price from various possible field names
        current_price = 0
        for opt in options_data:
            # Try different field names for underlying value
            if 'Underlying Value' in opt and opt['Underlying Value'] > 0:
                current_price = opt['Underlying Value']
                print(f"Found underlying value: {current_price}")
                break
            elif 'underlyingValue' in opt and opt['underlyingValue'] > 0:
                current_price = opt['underlyingValue']
                print(f"Found underlyingValue: {current_price}")
                break
            elif 'lastPrice' in opt and opt['lastPrice'] > 0:
                # This might be the underlying price in some API responses
                current_price = opt['lastPrice']
                print(f"Found lastPrice: {current_price}")
                break

        # If we still don't have a current price, try to estimate from strike prices
        if current_price == 0:
            current_price = sum(strikes) / len(strikes)
            print(f"Estimated current price from strikes: {current_price}")

        # Find ATM strike
        atm_strike = min(strikes, key=lambda x: abs(x - current_price))
        print(f"ATM strike: {atm_strike}")

        # Get ATM options - handle both uppercase and lowercase field names
        atm_options = []
        for opt in options_data:
            strike = opt.get('Strike Price', 0)
            if strike == 0:
                strike = opt.get('strikePrice', 0)
            if strike == atm_strike:
                atm_options.append(opt)

        print(f"Found {len(atm_options)} ATM options")

        # Extract IV values
        ivs = []

        # If we have Fyers API data, use the 'iv' field directly
        if has_fyers_iv:
            print("Using Fyers API IV data (lowercase 'iv' field)")
            for opt in options_data:
                # Get strike and option type, handling both formats
                strike = opt.get('Strike Price', 0)
                if strike == 0:
                    strike = opt.get('strikePrice', 0)

                option_type = opt.get('Option Type', '')
                if not option_type:
                    option_type = opt.get('optionType', '')

                # Get IV from Fyers API format (lowercase)
                iv = opt.get('iv', None)

                # If we have a valid IV, add it to the list
                if iv is not None and iv > 0:
                    ivs.append(iv)
                    print(f"Added Fyers IV value: {iv} from strike {strike} {option_type}")
        else:
            # Try to extract IV from ATM options first
            print(f"Looking for IV in {len(atm_options)} ATM options at strike {atm_strike}")

            for opt in atm_options:
                # Try all possible IV field names
                iv = opt.get('IV', None)
                if iv is None or iv == 0:
                    iv = opt.get('Implied Volatility', None)
                if iv is None or iv == 0:
                    iv = opt.get('ImpliedVolatility', None)
                if iv is None or iv == 0:
                    iv = opt.get('impliedVolatility', None)

                # If we have a valid IV, add it to the list
                if iv is not None and iv > 0:
                    ivs.append(iv)
                    print(f"Added IV value: {iv} from ATM option")

            # If no ATM options have IV, try to get IV from all options
            if not ivs:
                print("No IV found in ATM options, searching all options...")
                for opt in options_data:
                    # Get strike and option type, handling both formats
                    strike = opt.get('Strike Price', 0)
                    if strike == 0:
                        strike = opt.get('strikePrice', 0)

                    option_type = opt.get('Option Type', '')
                    if not option_type:
                        option_type = opt.get('optionType', '')

                    # Try all possible IV field names
                    iv = opt.get('IV', None)
                    if iv is None or iv == 0:
                        iv = opt.get('Implied Volatility', None)
                    if iv is None or iv == 0:
                        iv = opt.get('ImpliedVolatility', None)
                    if iv is None or iv == 0:
                        iv = opt.get('impliedVolatility', None)

                    # If we have a valid IV, add it to the list
                    if iv is not None and iv > 0:
                        ivs.append(iv)
                        print(f"Added IV value: {iv} from strike {strike} {option_type}")

        print(f"Total IV values found: {len(ivs)}")

        # If no IV values found, calculate IV using a simple estimation
        if not ivs:
            print("No valid IV found, using estimation based on option prices")

            # Try to calculate IV for ATM options
            for opt in atm_options:
                # Get strike and option type, handling both formats
                strike = opt.get('Strike Price', 0)
                if strike == 0:
                    strike = opt.get('strikePrice', 0)

                option_type = opt.get('Option Type', '')
                if not option_type:
                    option_type = opt.get('optionType', '')

                # Get price, handling both formats
                price = opt.get('LTP', 0)
                if price == 0:
                    price = opt.get('lastPrice', 0)

                if price > 0 and strike > 0:
                    try:
                        # Use a simple IV estimation based on option price and strike
                        price_to_strike_ratio = price / strike

                        # Rough IV estimate based on price/strike ratio
                        if option_type in ['CE', 'Call', 'call', 'C']:  # Call option
                            if price_to_strike_ratio < 0.01:
                                estimated_iv = 15.0  # Low IV for deep OTM
                            elif price_to_strike_ratio < 0.05:
                                estimated_iv = 25.0  # Medium IV for OTM
                            else:
                                estimated_iv = 35.0  # Higher IV for ATM/ITM
                        else:  # Put option
                            if price_to_strike_ratio < 0.01:
                                estimated_iv = 15.0  # Low IV for deep OTM
                            elif price_to_strike_ratio < 0.05:
                                estimated_iv = 25.0  # Medium IV for OTM
                            else:
                                estimated_iv = 35.0  # Higher IV for ATM/ITM

                        ivs.append(estimated_iv)
                        print(f"Estimated IV for {option_type} @ {strike}: {estimated_iv}%")
                    except Exception as e:
                        print(f"Error calculating IV: {e}")

        # If still no IV values, use default
        if not ivs:
            print("Using default IV value")
            default_iv = 30.0
            ivs = [default_iv]

            return {
                "current_iv": default_iv,
                "iv_percentile": 50.0,  # Middle of the range
                "analysis": "Using default IV value (30%) as no valid IV data available"
            }

        # Calculate average IV
        current_iv = sum(ivs) / len(ivs)
        print(f"Average IV: {current_iv:.2f}%")

        # If historical IV is provided, calculate percentile
        if historical_iv and len(historical_iv) > 0:
            # Calculate what percentage of historical values are below current IV
            below_count = sum(1 for iv in historical_iv if iv < current_iv)
            iv_percentile = (below_count / len(historical_iv)) * 100
            print(f"Calculated IV percentile from historical data: {iv_percentile:.2f}%")
        else:
            # Without historical data, use a default range based on market conditions
            # For index options, normal IV range is 15-45%
            # For stock options, normal IV range is 20-60%
            min_iv = 15
            max_iv = 45

            # Adjust range based on option type (if we can determine it's a stock)
            symbol = ""
            for opt in options_data[:5]:
                if 'symbol' in opt:
                    symbol = opt.get('symbol', '')
                    break
                elif 'Symbol' in opt:
                    symbol = opt.get('Symbol', '')
                    break

            if symbol and not any(index in symbol.upper() for index in ['NIFTY', 'BANKNIFTY', 'FINNIFTY']):
                min_iv = 20
                max_iv = 60
                print(f"Using stock option IV range: {min_iv}-{max_iv}%")
            else:
                print(f"Using index option IV range: {min_iv}-{max_iv}%")

            iv_percentile = max(0, min(100, (current_iv - min_iv) / (max_iv - min_iv) * 100))
            print(f"Calculated IV percentile from default range: {iv_percentile:.2f}%")

        # Generate analysis text
        if iv_percentile > 70:
            analysis = f"High IV Percentile ({iv_percentile:.1f}%) suggests options are expensive, favor selling strategies"
        elif iv_percentile < 30:
            analysis = f"Low IV Percentile ({iv_percentile:.1f}%) suggests options are cheap, favor buying strategies"
        else:
            analysis = f"Mid-range IV Percentile ({iv_percentile:.1f}%) suggests normal volatility conditions"

        print(f"IV Analysis: {analysis}")

        return {
            "current_iv": current_iv,
            "iv_percentile": iv_percentile,
            "analysis": analysis
        }

    def generate_trade_recommendations(self, options_data, current_price, symbol, days_to_expiry=None, institutional_analysis=None, money_flow_data=None):
        """
        Generate a single, high-quality trade recommendation based on deep options analysis
        with integrated institutional analysis and money flow data

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            symbol (str): Symbol of the underlying
            days_to_expiry (int or str, optional): Days to expiration. Defaults to None.
            institutional_analysis (dict, optional): Institutional analysis results. Defaults to None.
            money_flow_data (dict, optional): Money flow data. Defaults to None.

        Returns:
            dict: Trade recommendation with detailed analysis
        """
        print(f"\n--- Generating focused trade recommendation for {symbol} at price {current_price} ---")

        # Import enhanced scoring system
        from enhanced_scoring import EnhancedScoring
        enhanced_scorer = EnhancedScoring()

        # Ensure current_price is a float
        try:
            current_price = float(current_price)
        except (ValueError, TypeError):
            current_price = 0.0

        # Ensure days_to_expiry is properly handled
        days_to_expiry_int = None
        if days_to_expiry is not None:
            try:
                days_to_expiry_int = int(days_to_expiry)
            except (ValueError, TypeError):
                # If it's a date string, try to parse it
                try:
                    if isinstance(days_to_expiry, str) and '-' in days_to_expiry:
                        expiry_date = datetime.strptime(days_to_expiry, '%d-%m-%Y')
                        current_date = datetime.now()
                        days_to_expiry_int = (expiry_date - current_date).days
                        if days_to_expiry_int < 0:
                            days_to_expiry_int = 0
                except Exception:
                    days_to_expiry_int = None

        # Get overall market bias
        analysis_results = self.run_all_analysis(options_data, current_price, days_to_expiry_int)
        # Add days_to_expiry to analysis results for use in detailed analysis
        analysis_results["days_to_expiry"] = days_to_expiry_int

        # Integrate institutional analysis if available
        if institutional_analysis:
            analysis_results["institutional_analysis"] = institutional_analysis

            # Adjust market bias based on institutional analysis
            if institutional_analysis.get("combined_analysis"):
                # Add weight to institutional signals
                signals = analysis_results.get("signals", [])

                # Check for volume-OI spikes
                volume_oi = institutional_analysis.get("volume_oi_spikes", {})
                if volume_oi and volume_oi.get("success", False):
                    if "bullish" in volume_oi.get("summary", "").lower():
                        signals.append({"name": "Institutional Volume-OI", "bias": "Bullish", "weight": 20})
                    elif "bearish" in volume_oi.get("summary", "").lower():
                        signals.append({"name": "Institutional Volume-OI", "bias": "Bearish", "weight": 20})
                    else:
                        signals.append({"name": "Institutional Volume-OI", "bias": "Neutral", "weight": 10})

                # Check for dealer gamma
                dealer_gamma = institutional_analysis.get("dealer_gamma", {})
                if dealer_gamma and dealer_gamma.get("success", False):
                    if dealer_gamma.get("dealer_positioning", "").lower().startswith("positive"):
                        signals.append({"name": "Dealer Gamma", "bias": "Bullish", "weight": 15})
                    elif dealer_gamma.get("dealer_positioning", "").lower().startswith("negative"):
                        signals.append({"name": "Dealer Gamma", "bias": "Bearish", "weight": 15})

                # Check for delta-weighted OI
                delta_oi = institutional_analysis.get("delta_weighted_oi", {})
                if delta_oi and delta_oi.get("success", False):
                    if delta_oi.get("total_delta_weighted_oi", 0) > 0:
                        signals.append({"name": "Delta-Weighted OI", "bias": "Bullish", "weight": 15})
                    elif delta_oi.get("total_delta_weighted_oi", 0) < 0:
                        signals.append({"name": "Delta-Weighted OI", "bias": "Bearish", "weight": 15})

                # Check for smart money
                smart_money = institutional_analysis.get("smart_money", {})
                if smart_money and smart_money.get("success", False) and smart_money.get("analysis"):
                    if "bullish" in smart_money.get("analysis", "").lower():
                        signals.append({"name": "Smart Money", "bias": "Bullish", "weight": 20})
                    elif "bearish" in smart_money.get("analysis", "").lower():
                        signals.append({"name": "Smart Money", "bias": "Bearish", "weight": 20})

                # Recalculate overall bias with institutional signals
                analysis_results["signals"] = signals

                # Calculate overall bias
                bullish_weight = sum([s["weight"] for s in signals if s["bias"] == "Bullish"])
                bearish_weight = sum([s["weight"] for s in signals if s["bias"] == "Bearish"])
                neutral_weight = sum([s["weight"] for s in signals if s["bias"] == "Neutral"])

                total_weight = bullish_weight + bearish_weight + neutral_weight

                # Debug print to check weights
                print(f"Weights - Bullish: {bullish_weight}, Bearish: {bearish_weight}, Neutral: {neutral_weight}, Total: {total_weight}")

                if bullish_weight > bearish_weight and bullish_weight > neutral_weight:
                    overall_bias = "Bullish"
                    confidence = (bullish_weight / total_weight) * 100
                elif bearish_weight > bullish_weight and bearish_weight > neutral_weight:
                    overall_bias = "Bearish"
                    confidence = (bearish_weight / total_weight) * 100
                else:
                    overall_bias = "Neutral"
                    confidence = (neutral_weight / total_weight) * 100

                # Debug print to check confidence calculation
                print(f"Calculated confidence: {confidence}%")

                # Constrain confidence between 30-90%
                confidence = max(30, min(90, confidence))

                # Debug print to check final confidence
                print(f"Final confidence after constraints: {confidence}%")

                # Update analysis results
                analysis_results["overall_bias"] = overall_bias
                analysis_results["confidence"] = confidence

        # Get the final bias and confidence
        overall_bias = analysis_results["overall_bias"]
        confidence = analysis_results["confidence"]

        print(f"Market bias: {overall_bias} with {confidence:.1f}% confidence")

        # Group options by strike price
        strikes = {}
        for option in options_data:
            strike = option.get('Strike Price', 0)
            if strike == 0:
                strike = option.get('strikePrice', 0)

            option_type = option.get('Option Type', '')
            if not option_type:
                option_type = option.get('optionType', '')

            if strike not in strikes:
                strikes[strike] = {'CE': None, 'PE': None}

            if option_type in ['CE', 'PE', 'Call', 'Put', 'call', 'put', 'C', 'P']:
                # Normalize option type
                normalized_type = 'CE' if option_type in ['CE', 'Call', 'call', 'C'] else 'PE'
                strikes[strike][normalized_type] = option

        # Find ATM strike (closest to current price)
        if strikes:
            atm_strike = min(strikes.keys(), key=lambda x: abs(x - current_price))
            print(f"ATM strike: {atm_strike}")
        else:
            print("No valid strikes found")
            atm_strike = current_price

        # Calculate ATR (Average True Range) - using a placeholder value
        # In a real implementation, this would be calculated from historical price data
        atr = current_price * 0.01  # Assuming 1% of current price as ATR
        print(f"Using ATR value: {atr:.2f}")

        # Collect all options with their data
        all_options = []

        for strike, options in strikes.items():
            for option_type, option in options.items():
                if option:
                    # Skip if missing critical data
                    if not self._has_required_data(option):
                        continue

                    # Calculate distance from ATM as percentage
                    distance_pct = abs(strike - current_price) / current_price

                    # Determine if ATM, ITM, or OTM
                    if distance_pct < 0.01:
                        moneyness = "ATM"
                    elif (option_type == 'CE' and strike < current_price) or (option_type == 'PE' and strike > current_price):
                        moneyness = "ITM"
                    else:
                        moneyness = "OTM"

                    # Add option with additional metadata
                    all_options.append({
                        'strike': strike,
                        'option_type': option_type,
                        'option_data': option,
                        'distance_pct': distance_pct,
                        'moneyness': moneyness
                    })

        # Sort options by volume (primary) and OI change (secondary)
        all_options.sort(key=lambda x: (
            x['option_data'].get('Volume', 0) if isinstance(x['option_data'].get('Volume', 0), (int, float)) else 0,
            x['option_data'].get('Change in OI', 0) if isinstance(x['option_data'].get('Change in OI', 0), (int, float)) else 0
        ), reverse=True)

        print(f"Found {len(all_options)} valid options for analysis")

        # Calculate comprehensive scores for each option
        for option_info in all_options:
            option = option_info['option_data']
            strike = option_info['strike']
            option_type = option_info['option_type']

            # Calculate base score
            base_score = self._calculate_option_score(option, option_type, overall_bias, current_price, strike)

            # Calculate additional metrics
            ltp = option.get('LTP', 0)
            if ltp == 0:
                ltp = option.get('lastPrice', 0)

            volume = option.get('Volume', 0)
            if volume == 0:
                volume = option.get('volume', 0)

            oi_change = option.get('Change in OI', 0)
            if oi_change == 0:
                oi_change = option.get('oiChange', 0)

            price_change = option.get('Change', 0)
            if price_change == 0:
                price_change = option.get('change', 0)

            # Get IV (implied volatility)
            iv = option.get('IV', 0)
            if iv == 0:
                iv = option.get('iv', 0)
            if iv == 0:
                iv = option.get('Implied Volatility', 0)
            if iv == 0:
                iv = option.get('impliedVolatility', 0)

            # Use the IV from the option data, don't default to a constant
            # If IV is still 0, we'll use a dynamic value based on moneyness
            if iv == 0:
                # Calculate a dynamic IV based on moneyness
                if option_info['moneyness'] == "ATM":
                    iv = 25  # Base ATM IV
                elif option_info['moneyness'] == "OTM":
                    # OTM options typically have higher IV (volatility smile)
                    distance_factor = abs(strike - current_price) / current_price if current_price > 0 else 0
                    iv = 25 + (distance_factor * 100)  # Increases with distance from ATM
                else:  # ITM
                    # ITM options typically have lower IV
                    distance_factor = abs(strike - current_price) / current_price if current_price > 0 else 0
                    iv = 25 - (distance_factor * 20)  # Decreases with distance from ATM

                # Ensure IV is within reasonable bounds
                iv = max(15, min(60, iv))

                print(f"Using calculated IV for {option_type} @ {strike}: {iv:.2f}%")

            # Calculate target and stop loss
            if ltp > 0:
                iv_decimal = iv / 100  # Convert to decimal

                # More sophisticated target calculation based on moneyness and IV
                if option_info['moneyness'] == "ATM":
                    # ATM options have more predictable movement
                    target_price = ltp * (1.3 + (iv_decimal * 0.5))
                    stop_loss = ltp * (0.85 - (iv_decimal * 0.1))
                elif option_info['moneyness'] == "OTM":
                    # OTM options can have larger percentage moves
                    target_price = ltp * (1.5 + (iv_decimal * 0.7))
                    stop_loss = ltp * (0.75 - (iv_decimal * 0.1))
                else:  # ITM
                    # ITM options tend to have smaller percentage moves
                    target_price = ltp * (1.2 + (iv_decimal * 0.3))
                    stop_loss = ltp * (0.9 - (iv_decimal * 0.05))

                # Ensure stop loss is not too tight or too wide
                min_stop = ltp * 0.7
                max_stop = ltp * 0.9
                stop_loss = max(min(stop_loss, max_stop), min_stop)

                # Calculate risk-reward ratio
                risk = ltp - stop_loss
                reward = target_price - ltp
                risk_reward = round(reward / risk, 2) if risk > 0 else 0

                # Store calculated metrics
                option_info['ltp'] = ltp
                option_info['target_price'] = target_price
                option_info['stop_loss'] = stop_loss
                option_info['risk_reward'] = risk_reward
                option_info['iv'] = iv
                option_info['base_score'] = base_score

                # Calculate final score with additional factors
                liquidity_score = min(volume / 100000, 2) if volume > 0 else 0
                momentum_score = min(abs(price_change) / 10, 1) if price_change > 0 else 0
                risk_reward_score = min(risk_reward, 2)

                # Bias alignment bonus
                bias_alignment = 0
                if (option_type == 'CE' and overall_bias == 'Bullish') or (option_type == 'PE' and overall_bias == 'Bearish'):
                    bias_alignment = confidence / 100  # 0 to 1 based on confidence

                # Calculate final score (max 10)
                final_score = base_score + liquidity_score + momentum_score + risk_reward_score + bias_alignment
                final_score = min(max(final_score, 0), 10)  # Constrain between 0-10

                option_info['final_score'] = final_score

                print(f"{option_type} @ {strike} ({option_info['moneyness']}): Score {final_score:.2f}, R:R {risk_reward:.2f}, Volume {volume}")

        # Filter options with minimum criteria
        valid_options = [opt for opt in all_options if
                        opt.get('final_score', 0) >= 4.0 and  # Minimum score
                        opt.get('risk_reward', 0) >= 1.5 and  # Minimum risk-reward
                        opt.get('ltp', 0) > 0]                # Valid price

        # Sort by final score
        valid_options.sort(key=lambda x: x.get('final_score', 0), reverse=True)

        # If no valid options, return empty recommendations
        if not valid_options:
            print("No valid trade recommendations found")
            return {
                "calls": [],
                "puts": [],
                "market_bias": overall_bias,
                "confidence": confidence,
                "best_trade": None
            }

        # Get the best option
        best_option = valid_options[0]
        option = best_option['option_data']
        strike = best_option['strike']
        option_type = best_option['option_type']

        # Create detailed recommendation for the best option
        detailed_analysis = self._generate_detailed_analysis(
            best_option,
            current_price,
            atm_strike,
            overall_bias,
            confidence,
            analysis_results
        )

        # Create recommendation object
        best_recommendation = {
            'symbol': symbol,
            'strike': strike,
            'option_type': 'CALL' if option_type == 'CE' else 'PUT',
            'moneyness': best_option['moneyness'],
            'score': round(best_option['final_score'], 2),
            'buy_price': best_option['ltp'],
            'exit_target': round(best_option['target_price'], 2),
            'stop_loss': round(best_option['stop_loss'], 2),
            'iv': best_option['iv'],
            'oi_change': option.get('Change in OI', 0),
            'volume': option.get('Volume', 0),
            'atr': round(atr, 2),
            'risk_reward': best_option['risk_reward'],
            'change_pct': option.get('Change', 0),
            'description': self._generate_recommendation_description(option, option_type, strike, atm_strike, best_option['risk_reward']),
            'detailed_analysis': detailed_analysis
        }

        print(f"Best trade: {option_type} @ {strike} with score {best_option['final_score']:.2f}")

        # Create separate lists for calls and puts (alternative recommendations)
        # Filter out the best option from the alternatives
        alternative_options = [opt for opt in valid_options[1:6] if opt['option_type'] != option_type or opt['strike'] != strike]

        # Create alternative recommendations (up to 2 for each type)
        calls = []
        puts = []

        call_count = 0
        put_count = 0

        for opt in alternative_options:
            if call_count < 2 and opt['option_type'] == 'CE':
                # Create recommendation for call
                call_rec = {
                    'symbol': symbol,
                    'strike': opt['strike'],
                    'option_type': 'CALL',
                    'moneyness': opt['moneyness'],
                    'score': round(opt['final_score'], 2),
                    'buy_price': opt['ltp'],
                    'exit_target': round(opt['target_price'], 2),
                    'stop_loss': round(opt['stop_loss'], 2),
                    'iv': opt['iv'],
                    'oi_change': opt['option_data'].get('Change in OI', 0),
                    'volume': opt['option_data'].get('Volume', 0),
                    'atr': round(atr, 2),
                    'risk_reward': opt['risk_reward'],
                    'change_pct': opt['option_data'].get('Change', 0),
                    'description': self._generate_recommendation_description(opt['option_data'], opt['option_type'], opt['strike'], atm_strike, opt['risk_reward'])
                }
                calls.append(call_rec)
                call_count += 1

            elif put_count < 2 and opt['option_type'] == 'PE':
                # Create recommendation for put
                put_rec = {
                    'symbol': symbol,
                    'strike': opt['strike'],
                    'option_type': 'PUT',
                    'moneyness': opt['moneyness'],
                    'score': round(opt['final_score'], 2),
                    'buy_price': opt['ltp'],
                    'exit_target': round(opt['target_price'], 2),
                    'stop_loss': round(opt['stop_loss'], 2),
                    'iv': opt['iv'],
                    'oi_change': opt['option_data'].get('Change in OI', 0),
                    'volume': opt['option_data'].get('Volume', 0),
                    'atr': round(atr, 2),
                    'risk_reward': opt['risk_reward'],
                    'change_pct': opt['option_data'].get('Change', 0),
                    'description': self._generate_recommendation_description(opt['option_data'], opt['option_type'], opt['strike'], atm_strike, opt['risk_reward'])
                }
                puts.append(put_rec)
                put_count += 1

            # Stop once we have 2 of each
            if call_count >= 2 and put_count >= 2:
                break

        # Create initial recommendations
        initial_recommendations = {
            "calls": calls,
            "puts": puts,
            "market_bias": overall_bias,
            "confidence": confidence,
            "best_trade": best_recommendation
        }

        # Apply enhanced scoring and filtering
        enhanced_recommendations = enhanced_scorer.enhance_trade_recommendations(
            initial_recommendations,
            symbol,
            options_data,
            current_price,
            days_to_expiry
        )

        # Add money flow data to each recommendation if available
        if money_flow_data and money_flow_data.get('success', False):
            # Add money flow data to best trade
            if enhanced_recommendations.get('best_trade'):
                flow_bias = money_flow_data.get('flow_bias', 'Neutral')
                flow_strength = money_flow_data.get('flow_strength', 'Moderate')

                # Check if money flow aligns with option type
                option_type = enhanced_recommendations['best_trade']['option_type']
                alignment = (flow_bias == 'Bullish' and option_type == 'CALL') or (flow_bias == 'Bearish' and option_type == 'PUT')

                enhanced_recommendations['best_trade']['money_flow'] = {
                    'bias': flow_bias,
                    'strength': flow_strength,
                    'alignment': alignment,
                    'fii_net': money_flow_data.get('institutional_activity', {}).get('fii', {}).get('net', 0),
                    'dii_net': money_flow_data.get('institutional_activity', {}).get('dii', {}).get('net', 0),
                    'delivery_percentage': money_flow_data.get('delivery_percentage', 0),
                    'futures_oi_change': money_flow_data.get('futures_oi', {}).get('change_percent', 0)
                }

                # Update description with money flow insight
                if alignment:
                    enhanced_recommendations['best_trade']['description'] += f" Money flow is {flow_bias.lower()} ({flow_strength}), aligning with this trade."
                else:
                    enhanced_recommendations['best_trade']['description'] += f" Note: Money flow is {flow_bias.lower()} ({flow_strength})."

            # Add money flow data to alternative recommendations
            for rec_list in [enhanced_recommendations.get('calls', []), enhanced_recommendations.get('puts', [])]:
                for rec in rec_list:
                    option_type = rec['option_type']
                    flow_bias = money_flow_data.get('flow_bias', 'Neutral')
                    flow_strength = money_flow_data.get('flow_strength', 'Moderate')

                    # Check if money flow aligns with option type
                    alignment = (flow_bias == 'Bullish' and option_type == 'CALL') or (flow_bias == 'Bearish' and option_type == 'PUT')

                    rec['money_flow'] = {
                        'bias': flow_bias,
                        'strength': flow_strength,
                        'alignment': alignment,
                        'fii_net': money_flow_data.get('institutional_activity', {}).get('fii', {}).get('net', 0),
                        'dii_net': money_flow_data.get('institutional_activity', {}).get('dii', {}).get('net', 0),
                        'delivery_percentage': money_flow_data.get('delivery_percentage', 0),
                        'futures_oi_change': money_flow_data.get('futures_oi', {}).get('change_percent', 0)
                    }

                    # Update description with money flow insight
                    if alignment:
                        rec['description'] += f" Money flow is {flow_bias.lower()}, aligning with this trade."

            # Add money flow analysis summary
            enhanced_recommendations['money_flow_analysis'] = money_flow_data.get('analysis', '')

        # Return the enhanced recommendations
        return enhanced_recommendations

    def _has_required_data(self, option):
        """Check if option has required data for analysis"""
        # Check for price
        has_price = option.get('LTP', 0) > 0 or option.get('lastPrice', 0) > 0

        # Check for volume
        has_volume = option.get('Volume', 0) > 0 or option.get('volume', 0) > 0

        return has_price and has_volume

    def _generate_detailed_analysis(self, option_info, current_price, atm_strike, market_bias, confidence, analysis_results):
        """Generate detailed analysis for a trade recommendation"""
        option = option_info['option_data']
        strike = option_info['strike']
        option_type = option_info['option_type']
        moneyness = option_info['moneyness']

        # Get option data
        ltp = option_info['ltp']
        volume = option.get('Volume', 0) if isinstance(option.get('Volume', 0), (int, float)) else 0
        oi_change = option.get('Change in OI', 0) if isinstance(option.get('Change in OI', 0), (int, float)) else 0
        price_change = option.get('Change', 0) if isinstance(option.get('Change', 0), (int, float)) else 0
        iv = option_info['iv']

        # Format option type for display
        display_type = "Call" if option_type == 'CE' else "Put"

        # Calculate distance from current price
        distance_pct = abs(strike - current_price) / current_price
        distance_formatted = f"{distance_pct:.2%}"

        # Get market data from analysis
        max_pain = analysis_results.get('max_pain', {}).get('max_pain_strike', 0)
        pcr = analysis_results.get('pcr', {}).get('pcr', 0)

        # Try to get days to expiry from analysis results
        days_to_expiry = analysis_results.get('days_to_expiry', None)

        # Build detailed analysis
        analysis = []

        # 1. Trade summary
        analysis.append(f"TRADE SUMMARY: {display_type.upper()} option at strike {strike} ({moneyness})")
        analysis.append(f"Entry: ₹{ltp:.2f} | Target: ₹{option_info['target_price']:.2f} | Stop Loss: ₹{option_info['stop_loss']:.2f}")
        analysis.append(f"Risk-Reward Ratio: 1:{option_info['risk_reward']} | Score: {option_info['final_score']:.2f}/10")
        analysis.append("")

        # 2. Market context
        analysis.append("MARKET CONTEXT:")
        analysis.append(f"• Current Price: ₹{current_price:.2f} | ATM Strike: {atm_strike}")
        analysis.append(f"• Market Bias: {market_bias} with {confidence:.1f}% confidence")
        analysis.append(f"• Max Pain Point: {max_pain} ({'+' if max_pain > current_price else '-'}{abs(max_pain - current_price):.2f} from current)")
        analysis.append(f"• Put-Call Ratio: {pcr:.2f} ({'Bullish' if pcr > 1.2 else 'Bearish' if pcr < 0.8 else 'Neutral'})")

        # Add institutional analysis if available
        institutional_analysis = analysis_results.get('institutional_analysis', None)
        if institutional_analysis:
            analysis.append("")
            analysis.append("INSTITUTIONAL ANALYSIS:")

            # Volume-OI spikes
            volume_oi = institutional_analysis.get("volume_oi_spikes", {})
            if volume_oi and volume_oi.get("success", False):
                analysis.append(f"• Volume-OI: {volume_oi.get('summary', 'No data')}")

            # Dealer gamma
            dealer_gamma = institutional_analysis.get("dealer_gamma", {})
            if dealer_gamma and dealer_gamma.get("success", False):
                analysis.append(f"• Dealer Gamma: {dealer_gamma.get('dealer_positioning', 'No data')}")

            # IV skew
            iv_skew = institutional_analysis.get("iv_skew", {})
            if iv_skew and iv_skew.get("success", False):
                analysis.append(f"• IV Skew: {iv_skew.get('skew_direction', 'No data')}")

            # Delta-weighted OI
            delta_oi = institutional_analysis.get("delta_weighted_oi", {})
            if delta_oi and delta_oi.get("success", False):
                delta_value = delta_oi.get("total_delta_weighted_oi", 0)
                analysis.append(f"• Delta-Weighted OI: {delta_value:,.0f} ({'Bullish' if delta_value > 0 else 'Bearish' if delta_value < 0 else 'Neutral'})")

            # Smart money
            smart_money = institutional_analysis.get("smart_money", {})
            if smart_money and smart_money.get("success", False):
                analysis.append(f"• Smart Money: {smart_money.get('analysis', 'No data')}")

        analysis.append("")

        # 3. Option characteristics
        analysis.append("OPTION CHARACTERISTICS:")
        analysis.append(f"• Strike Distance: {distance_formatted} from current price")
        analysis.append(f"• Implied Volatility: {iv:.2f}%")
        analysis.append(f"• Volume: {volume:,} contracts")
        analysis.append(f"• OI Change: {oi_change:,} contracts")
        analysis.append(f"• Price Change: {price_change:.2f}%")
        analysis.append("")

        # 4. Trade rationale
        analysis.append("TRADE RATIONALE:")

        # Volume analysis
        if volume > 1000000:
            analysis.append(f"• Extremely high volume ({volume:,}) indicates strong institutional interest")
        elif volume > 500000:
            analysis.append(f"• Very high volume ({volume:,}) suggests significant market activity")
        elif volume > 100000:
            analysis.append(f"• Good volume ({volume:,}) provides adequate liquidity")

        # OI analysis
        if oi_change > 100000:
            analysis.append(f"• Massive OI buildup ({oi_change:,}) shows strong institutional positioning")
        elif oi_change > 50000:
            analysis.append(f"• Significant OI increase ({oi_change:,}) indicates new positions being built")
        elif oi_change > 10000:
            analysis.append(f"• Moderate OI change ({oi_change:,}) shows ongoing interest")
        elif oi_change < -50000:
            analysis.append(f"• Large OI unwinding ({oi_change:,}) suggests positions being closed")

        # Price momentum
        if price_change > 20:
            analysis.append(f"• Strong upward momentum ({price_change:.2f}%) indicates bullish sentiment")
        elif price_change > 10:
            analysis.append(f"• Good price action ({price_change:.2f}%) shows positive momentum")
        elif price_change < -15:
            analysis.append(f"• Sharp decline ({price_change:.2f}%) may present contrarian opportunity")

        # Strike selection rationale
        if moneyness == "ATM":
            analysis.append(f"• ATM strike provides balanced risk-reward with higher delta")
        elif moneyness == "OTM":
            if distance_pct < 0.05:
                analysis.append(f"• Slightly OTM strike offers leverage with reasonable probability")
            else:
                analysis.append(f"• OTM strike provides high leverage but lower probability")
        else:  # ITM
            analysis.append(f"• ITM strike offers higher probability but less leverage")

        # Market bias alignment
        if (option_type == 'CE' and market_bias == 'Bullish') or (option_type == 'PE' and market_bias == 'Bearish'):
            analysis.append(f"• Trade aligns with overall {market_bias.lower()} market bias ({confidence:.1f}% confidence)")
        elif (option_type == 'CE' and market_bias == 'Bearish') or (option_type == 'PE' and market_bias == 'Bullish'):
            analysis.append(f"• Counter-trend trade against {market_bias.lower()} market bias")

        # Max pain alignment
        if option_type == 'CE' and max_pain < current_price:
            analysis.append(f"• Max pain at {max_pain} supports bullish bias (below current price)")
        elif option_type == 'PE' and max_pain > current_price:
            analysis.append(f"• Max pain at {max_pain} supports bearish bias (above current price)")

        analysis.append("")

        # 5. Risk factors
        analysis.append("RISK FACTORS:")

        # IV risk
        if iv > 50:
            analysis.append(f"• High IV ({iv:.2f}%) increases premium cost and vulnerability to IV crush")
        elif iv < 20:
            analysis.append(f"• Low IV ({iv:.2f}%) may limit option's response to price movement")

        # Time decay - only add if days_to_expiry is available
        if days_to_expiry:
            if days_to_expiry < 5:
                analysis.append(f"• Very short time to expiry ({days_to_expiry} days) accelerates time decay")
            elif days_to_expiry < 10:
                analysis.append(f"• Short time to expiry ({days_to_expiry} days) increases time decay risk")
        else:
            # Add a generic time decay warning if we don't know days to expiry
            analysis.append("• Time decay (theta) will erode option value as expiry approaches")

        # Counter-trend risk
        if (option_type == 'CE' and market_bias == 'Bearish') or (option_type == 'PE' and market_bias == 'Bullish'):
            analysis.append(f"• Trade is counter to the overall market bias ({market_bias})")

        # Liquidity risk
        if volume < 50000:
            analysis.append(f"• Moderate volume ({volume:,}) may present liquidity challenges")

        # Join all analysis points
        return "\n".join(analysis)

    def _calculate_option_score(self, option, option_type, market_bias, current_price, strike):
        """
        Calculate a score for an option based on various factors with emphasis on momentum

        Args:
            option (dict): Option data
            option_type (str): 'CE' for call or 'PE' for put
            market_bias (str): Overall market bias
            current_price (float): Current price of the underlying
            strike (float): Strike price of the option

        Returns:
            float: Score between 0 and 5
        """
        score = 0

        # Print option data for debugging
        print(f"Scoring option: {option_type} @ {strike}, LTP: {option.get('LTP', 0)}, Volume: {option.get('Volume', 0)}, OI Change: {option.get('Change in OI', 0)}")

        # Volume factor - primary indicator of interest and liquidity
        volume = option.get('Volume', 0)
        if volume > 150000:
            score += 2.25  # Extremely high volume - strong institutional interest
            print(f"  +2.25 for extremely high volume: {volume}")
        elif volume > 75000:
            score += 1.75  # Very high volume
            print(f"  +1.75 for very high volume: {volume}")
        elif volume > 25000:
            score += 1.25  # High volume
            print(f"  +1.25 for high volume: {volume}")
        elif volume > 5000:
            score += 0.5  # Moderate volume
            print(f"  +0.5 for moderate volume: {volume}")
        elif volume < 1000:
            score -= 1.5  # Very low volume - liquidity risk
            print(f"  -1.5 for very low volume: {volume}")
        elif volume < 2500:
            score -= 0.75  # Low volume
            print(f"  -0.75 for low volume: {volume}")

        # OI change factor - indicates institutional interest and positioning
        oi_change = option.get('Change in OI', 0)
        oi = option.get('OI', 0)

        # Calculate OI change percentage for better comparison across different stocks
        oi_change_pct = (oi_change / oi * 100) if oi > 0 else 0

        if oi_change > 15000 or oi_change_pct > 15:
            score += 1.5  # Very strong OI increase - significant institutional positioning
            print(f"  +1.5 for very strong OI increase: {oi_change} ({oi_change_pct:.1f}%)")
        elif oi_change > 7500 or oi_change_pct > 7.5:
            score += 1.0  # Strong OI increase
            print(f"  +1.0 for strong OI increase: {oi_change} ({oi_change_pct:.1f}%)")
        elif oi_change > 3000 or oi_change_pct > 3:
            score += 0.5  # Moderate OI increase
            print(f"  +0.5 for moderate OI increase: {oi_change} ({oi_change_pct:.1f}%)")
        elif oi_change < -10000 or oi_change_pct < -10:
            score -= 1.0  # Strong OI decrease - unwinding of positions
            print(f"  -1.0 for strong OI decrease: {oi_change} ({oi_change_pct:.1f}%)")
        elif oi_change < -5000 or oi_change_pct < -5:
            score -= 0.5  # Moderate OI decrease
            print(f"  -0.5 for moderate OI decrease: {oi_change} ({oi_change_pct:.1f}%)")

        # Price change factor - indicates momentum and directional strength
        price_change_pct = option.get('Change', 0)
        if price_change_pct > 30:
            score += 1.25  # Very strong price increase - powerful momentum
            print(f"  +1.25 for very strong price increase: {price_change_pct}%")
        elif price_change_pct > 15:
            score += 0.75  # Strong price increase
            print(f"  +0.75 for strong price increase: {price_change_pct}%")
        elif price_change_pct > 7.5:
            score += 0.25  # Moderate price increase
            print(f"  +0.25 for moderate price increase: {price_change_pct}%")
        elif price_change_pct < -30:
            score -= 0.75  # Very strong price decrease - negative momentum
            print(f"  -0.75 for very strong price decrease: {price_change_pct}%")
        elif price_change_pct < -15:
            score -= 0.25  # Strong price decrease
            print(f"  -0.25 for strong price decrease: {price_change_pct}%")

        # Market bias alignment - important for trend following
        if (option_type == 'CE' and market_bias == 'Bullish') or (option_type == 'PE' and market_bias == 'Bearish'):
            score += 0.75  # Strong alignment with market bias
            print(f"  +0.75 for strong alignment with market bias: {market_bias}")
        elif (option_type == 'CE' and market_bias == 'Bearish') or (option_type == 'PE' and market_bias == 'Bullish'):
            score -= 0.5  # Contrary to market bias - higher risk
            print(f"  -0.5 for contrary to market bias: {market_bias}")

        # Strike distance from current price - balance between premium cost and probability
        distance_pct = abs(strike - current_price) / current_price

        # For calls, check if strike is above or below current price
        if option_type == 'CE':
            if strike < current_price:  # ITM call
                if distance_pct < 0.01:  # Just ITM
                    score += 0.5  # Good balance of delta and premium
                    print(f"  +0.5 for slightly ITM call (distance: {distance_pct:.2%})")
                elif distance_pct > 0.05:  # Deep ITM
                    score -= 0.25  # High premium, mostly intrinsic value
                    print(f"  -0.25 for deep ITM call (distance: {distance_pct:.2%})")
            else:  # OTM call
                if distance_pct < 0.02:  # Just OTM
                    score += 0.75  # Good risk/reward for directional play
                    print(f"  +0.75 for slightly OTM call (distance: {distance_pct:.2%})")
                elif distance_pct > 0.1:  # Far OTM
                    score -= 0.75  # Low probability of profit
                    print(f"  -0.75 for far OTM call (distance: {distance_pct:.2%})")
                elif distance_pct > 0.05:  # Moderately OTM
                    score -= 0.25  # Lower probability but still reasonable
                    print(f"  -0.25 for moderately OTM call (distance: {distance_pct:.2%})")

        # For puts, check if strike is above or below current price
        else:  # PE
            if strike > current_price:  # ITM put
                if distance_pct < 0.01:  # Just ITM
                    score += 0.5  # Good balance of delta and premium
                    print(f"  +0.5 for slightly ITM put (distance: {distance_pct:.2%})")
                elif distance_pct > 0.05:  # Deep ITM
                    score -= 0.25  # High premium, mostly intrinsic value
                    print(f"  -0.25 for deep ITM put (distance: {distance_pct:.2%})")
            else:  # OTM put
                if distance_pct < 0.02:  # Just OTM
                    score += 0.75  # Good risk/reward for directional play
                    print(f"  +0.75 for slightly OTM put (distance: {distance_pct:.2%})")
                elif distance_pct > 0.1:  # Far OTM
                    score -= 0.75  # Low probability of profit
                    print(f"  -0.75 for far OTM put (distance: {distance_pct:.2%})")
                elif distance_pct > 0.05:  # Moderately OTM
                    score -= 0.25  # Lower probability but still reasonable
                    print(f"  -0.25 for moderately OTM put (distance: {distance_pct:.2%})")

        # Bid-Ask spread factor - critical for execution quality and liquidity
        bid = option.get('Bid', 0)
        ask = option.get('Ask', 0)
        if bid > 0 and ask > 0:
            spread_pct = (ask - bid) / ((bid + ask) / 2) if (bid + ask) > 0 else 1
            if spread_pct < 0.01:  # Very tight spread
                score += 0.75  # Excellent liquidity
                print(f"  +0.75 for very tight bid-ask spread: {spread_pct:.2%}")
            elif spread_pct < 0.03:  # Tight spread
                score += 0.5  # Good liquidity
                print(f"  +0.5 for tight bid-ask spread: {spread_pct:.2%}")
            elif spread_pct > 0.15:  # Very wide spread
                score -= 1.0  # Very poor liquidity - execution risk
                print(f"  -1.0 for very wide bid-ask spread: {spread_pct:.2%}")
            elif spread_pct > 0.08:  # Wide spread
                score -= 0.5  # Poor liquidity
                print(f"  -0.5 for wide bid-ask spread: {spread_pct:.2%}")

        # IV factor - check if IV is favorable for the strategy
        iv = option.get('IV', 0)
        if iv > 0:
            if option_type == 'CE' and market_bias == 'Bullish':
                if iv < 30:  # Low IV for bullish call strategy
                    score += 0.5  # Good for directional call buying
                    print(f"  +0.5 for low IV ({iv}%) in bullish market")
                elif iv > 60:  # High IV for bullish call strategy
                    score -= 0.25  # Expensive premium
                    print(f"  -0.25 for high IV ({iv}%) in bullish market")
            elif option_type == 'PE' and market_bias == 'Bearish':
                if iv < 30:  # Low IV for bearish put strategy
                    score += 0.5  # Good for directional put buying
                    print(f"  +0.5 for low IV ({iv}%) in bearish market")
                elif iv > 60:  # High IV for bearish put strategy
                    score -= 0.25  # Expensive premium
                    print(f"  -0.25 for high IV ({iv}%) in bearish market")

        # Final score
        final_score = max(0, min(5, score))
        print(f"  Final score: {final_score}")
        return final_score

    def _generate_recommendation_description(self, option, option_type, strike, atm_strike, risk_reward):
        """
        Generate a detailed description for the recommendation with focus on momentum indicators
        Enhanced with more sophisticated analysis and real-world trading insights

        Args:
            option (dict): Option data
            option_type (str): 'CE' for call or 'PE' for put
            strike (float): Strike price of the option
            atm_strike (float): ATM strike price
            risk_reward (float): Risk-reward ratio

        Returns:
            str: Description
        """
        option_name = "CALL" if option_type == 'CE' else "PUT"

        # Get option price
        ltp = option.get('LTP', 0)

        # Calculate distance from current price
        current_price = atm_strike  # Using ATM strike as proxy for current price
        distance_pct = abs(strike - current_price) / current_price * 100

        # Determine if ATM, ITM, or OTM with more precise categorization
        if abs(strike - atm_strike) < (atm_strike * 0.005):  # Within 0.5% of ATM
            moneyness = f"ATM {option_name}"
            moneyness_detail = f"(strike {strike}, very close to market price)"
        elif (option_type == 'CE' and strike < atm_strike) or (option_type == 'PE' and strike > atm_strike):
            # ITM options with degree of moneyness
            if distance_pct > 10:
                moneyness = f"Deep ITM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% ITM)"
            elif distance_pct > 5:
                moneyness = f"Moderately ITM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% ITM)"
            else:
                moneyness = f"Slightly ITM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% ITM)"
        else:
            # OTM options with degree of moneyness
            if distance_pct > 10:
                moneyness = f"Deep OTM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% OTM)"
            elif distance_pct > 5:
                moneyness = f"Moderately OTM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% OTM)"
            else:
                moneyness = f"Slightly OTM {option_name}"
                moneyness_detail = f"(strike {strike}, {distance_pct:.1f}% OTM)"

        # Volume analysis - primary momentum indicator with more detailed categorization
        volume = option.get('Volume', 0)
        volume_description = ""
        if volume > 150000:
            volume_description = f" with extremely high volume ({volume:,} contracts)"
        elif volume > 75000:
            volume_description = f" with very high volume ({volume:,} contracts)"
        elif volume > 25000:
            volume_description = f" with high volume ({volume:,} contracts)"
        elif volume > 10000:
            volume_description = f" with moderate volume ({volume:,} contracts)"
        elif volume < 2500:
            volume_description = f" with low volume ({volume:,} contracts) - liquidity caution"

        # Check OI change - institutional interest with percentage context
        oi_change = option.get('Change in OI', 0)
        oi = option.get('OI', 0)
        oi_change_pct = (oi_change / oi * 100) if oi > 0 else 0

        oi_description = ""
        if oi_change > 15000 or oi_change_pct > 15:
            oi_description = f" and very strong OI buildup ({oi_change:,} contracts, {oi_change_pct:.1f}% increase)"
        elif oi_change > 7500 or oi_change_pct > 7.5:
            oi_description = f" and strong OI buildup ({oi_change:,} contracts, {oi_change_pct:.1f}% increase)"
        elif oi_change > 3000 or oi_change_pct > 3:
            oi_description = f" and moderate OI buildup ({oi_change:,} contracts, {oi_change_pct:.1f}% increase)"
        elif oi_change < -10000 or oi_change_pct < -10:
            oi_description = f" with significant OI unwinding ({oi_change:,} contracts, {oi_change_pct:.1f}% decrease)"
        elif oi_change < -5000 or oi_change_pct < -5:
            oi_description = f" with moderate OI unwinding ({oi_change:,} contracts, {oi_change_pct:.1f}% decrease)"

        # Price change - momentum indicator with more context
        price_change = option.get('Change', 0)
        price_description = ""
        if price_change > 30:
            price_description = f" showing very strong momentum (+{price_change:.1f}%)"
        elif price_change > 15:
            price_description = f" showing strong momentum (+{price_change:.1f}%)"
        elif price_change > 7.5:
            price_description = f" showing moderate momentum (+{price_change:.1f}%)"
        elif price_change < -20:
            price_description = f" with significant price decline ({price_change:.1f}%)"
        elif price_change < -10:
            price_description = f" with moderate price decline ({price_change:.1f}%)"

        # Add risk-reward info with explanation
        risk_reward_text = f"Risk:Reward = 1:{risk_reward:.2f}"
        if risk_reward > 3:
            risk_reward_text += " (excellent potential return for risk)"
        elif risk_reward > 2:
            risk_reward_text += " (good potential return for risk)"
        elif risk_reward < 1:
            risk_reward_text += " (caution: potential return may not justify risk)"

        # Add IV info with context if available
        iv = option.get('IV', 0)
        iv_text = ""
        if iv > 0:
            if iv > 60:
                iv_text = f", IV: {iv:.1f}% (high - expensive premium)"
            elif iv > 40:
                iv_text = f", IV: {iv:.1f}% (moderate)"
            else:
                iv_text = f", IV: {iv:.1f}% (low - relatively cheap premium)"

        # Add liquidity assessment
        bid = option.get('Bid', 0)
        ask = option.get('Ask', 0)
        liquidity_text = ""
        if bid > 0 and ask > 0:
            spread_pct = (ask - bid) / ((bid + ask) / 2) if (bid + ask) > 0 else 1
            if spread_pct < 0.01:
                liquidity_text = ", excellent liquidity (very tight spread)"
            elif spread_pct < 0.03:
                liquidity_text = ", good liquidity"
            elif spread_pct > 0.15:
                liquidity_text = ", poor liquidity (wide spread) - execution caution"
            elif spread_pct > 0.08:
                liquidity_text = ", moderate liquidity concerns"

        # Combine all descriptions
        description = f"{moneyness} {moneyness_detail}{volume_description}{oi_description}{price_description}. {risk_reward_text}{iv_text}{liquidity_text}"

        # Add trade suggestion based on comprehensive analysis
        if (volume > 75000 or oi_change > 10000) and price_change > 15:
            description += " STRONG MOMENTUM TRADE with significant institutional interest - high conviction setup."
        elif (volume > 25000 or oi_change > 5000) and price_change > 7.5:
            description += " Good momentum trade with institutional positioning."
        elif volume > 10000 and oi_change > 1000:
            description += " Moderate momentum with decent volume."

        # Add specific strategy suggestions based on option characteristics
        if option_type == 'CE':
            if distance_pct < 2 and price_change > 0 and oi_change > 0:
                description += " Consider as a directional bullish play with good delta exposure."
            elif distance_pct > 5 and distance_pct < 10 and iv < 40:
                description += " Potential for leveraged upside if bullish momentum continues."
        else:  # Put options
            if distance_pct < 2 and price_change > 0 and oi_change > 0:
                description += " Consider as a directional bearish play with good delta exposure."
            elif distance_pct > 5 and distance_pct < 10 and iv < 40:
                description += " Potential for leveraged downside if bearish momentum continues."

        # Add price information
        description += f" Current premium: ₹{ltp}"

        return description

    def run_all_analysis(self, options_data, current_price, days_to_expiry=None):
        """
        Run all analysis techniques and return a comprehensive result

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            days_to_expiry (int or str, optional): Days to expiration. Defaults to None.

        Returns:
            dict: Comprehensive analysis results
        """
        # Ensure current_price is a float
        try:
            current_price = float(current_price)
        except (ValueError, TypeError):
            current_price = 0.0

        # Ensure days_to_expiry is properly handled
        days_to_expiry_int = None
        if days_to_expiry is not None:
            try:
                days_to_expiry_int = int(days_to_expiry)
            except (ValueError, TypeError):
                # If it's a date string, try to parse it
                try:
                    if isinstance(days_to_expiry, str) and '-' in days_to_expiry:
                        expiry_date = datetime.strptime(days_to_expiry, '%d-%m-%Y')
                        current_date = datetime.now()
                        days_to_expiry_int = (expiry_date - current_date).days
                        if days_to_expiry_int < 0:
                            days_to_expiry_int = 0
                except Exception:
                    days_to_expiry_int = None

        max_pain_results = self.calculate_max_pain(options_data, current_price)
        pcr_results = self.calculate_put_call_ratio(options_data)
        oi_change_results = self.calculate_oi_change_analysis(options_data)
        gex_results = self.calculate_gamma_exposure(options_data, current_price, days_to_expiry_int)
        iv_percentile_results = self.calculate_iv_percentile(options_data)

        # Determine overall market bias
        signals = []

        # Max Pain signal
        if max_pain_results["max_pain_strike"] > current_price:
            signals.append({"name": "Max Pain", "bias": "Bullish", "weight": 15})
        elif max_pain_results["max_pain_strike"] < current_price:
            signals.append({"name": "Max Pain", "bias": "Bearish", "weight": 15})
        else:
            signals.append({"name": "Max Pain", "bias": "Neutral", "weight": 15})

        # PCR signal
        if pcr_results["pcr"] > 1.3:
            signals.append({"name": "Put-Call Ratio", "bias": "Bullish", "weight": 15})
        elif pcr_results["pcr"] < 0.7:
            signals.append({"name": "Put-Call Ratio", "bias": "Bearish", "weight": 15})
        else:
            signals.append({"name": "Put-Call Ratio", "bias": "Neutral", "weight": 15})

        # OI Change signal
        if oi_change_results["call_oi_change"] > 0 and oi_change_results["put_oi_change"] < 0:
            signals.append({"name": "OI Change", "bias": "Bullish", "weight": 15})
        elif oi_change_results["put_oi_change"] > 0 and oi_change_results["call_oi_change"] < 0:
            signals.append({"name": "OI Change", "bias": "Bearish", "weight": 15})
        else:
            signals.append({"name": "OI Change", "bias": "Neutral", "weight": 15})

        # GEX signal
        if gex_results["net_gex"] > 500000:
            signals.append({"name": "Gamma Exposure", "bias": "Bullish", "weight": 15})
        elif gex_results["net_gex"] < -500000:
            signals.append({"name": "Gamma Exposure", "bias": "Bearish", "weight": 15})
        else:
            signals.append({"name": "Gamma Exposure", "bias": "Neutral", "weight": 15})

        # IV Percentile signal
        if iv_percentile_results["iv_percentile"] > 70:
            signals.append({"name": "IV Percentile", "bias": "Bearish", "weight": 10})  # High IV often precedes downward moves
        elif iv_percentile_results["iv_percentile"] < 30:
            signals.append({"name": "IV Percentile", "bias": "Bullish", "weight": 10})  # Low IV often precedes upward moves
        else:
            signals.append({"name": "IV Percentile", "bias": "Neutral", "weight": 10})

        # Calculate overall bias
        bullish_weight = sum([s["weight"] for s in signals if s["bias"] == "Bullish"])
        bearish_weight = sum([s["weight"] for s in signals if s["bias"] == "Bearish"])
        neutral_weight = sum([s["weight"] for s in signals if s["bias"] == "Neutral"])

        total_weight = bullish_weight + bearish_weight + neutral_weight

        # Debug print to check weights
        print(f"Weights - Bullish: {bullish_weight}, Bearish: {bearish_weight}, Neutral: {neutral_weight}, Total: {total_weight}")

        if bullish_weight > bearish_weight and bullish_weight > neutral_weight:
            overall_bias = "Bullish"
            confidence = (bullish_weight / total_weight) * 100
        elif bearish_weight > bullish_weight and bearish_weight > neutral_weight:
            overall_bias = "Bearish"
            confidence = (bearish_weight / total_weight) * 100
        else:
            overall_bias = "Neutral"
            confidence = (neutral_weight / total_weight) * 100

        # Debug print to check confidence calculation
        print(f"Calculated confidence: {confidence}%")

        # Constrain confidence between 30-90%
        confidence = max(30, min(90, confidence))

        # Debug print to check final confidence
        print(f"Final confidence after constraints: {confidence}%")

        return {
            "max_pain": max_pain_results,
            "pcr": pcr_results,
            "oi_change": oi_change_results,
            "gex": gex_results,
            "iv_percentile": iv_percentile_results,
            "overall_bias": overall_bias,
            "confidence": confidence,
            "signals": signals,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

# This module should be imported and used with real Fyers API data only

