<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Options Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
        }
        .analysis-card {
            height: 100%;
        }
        .analysis-value {
            font-size: 24px;
            font-weight: bold;
        }
        .bullish {
            color: #28a745;
        }
        .bearish {
            color: #dc3545;
        }
        .neutral {
            color: #6c757d;
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .signal-badge {
            font-size: 14px;
            padding: 5px 10px;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-block;
        }
        .confidence-meter {
            height: 10px;
            border-radius: 5px;
            background-color: #e9ecef;
            margin-top: 5px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .confidence-value {
            height: 100%;
            border-radius: 5px;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Trade recommendation styles */
        .recommendation-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .recommendation-header {
            padding: 10px 15px;
            font-weight: bold;
            font-size: 18px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .call-header {
            background-color: rgba(54, 162, 235, 0.2);
            border-bottom: 2px solid rgba(54, 162, 235, 0.8);
        }

        .put-header {
            background-color: rgba(255, 99, 132, 0.2);
            border-bottom: 2px solid rgba(255, 99, 132, 0.8);
        }

        .recommendation-body {
            padding: 15px;
        }

        .recommendation-score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .recommendation-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .recommendation-label {
            font-weight: 500;
            color: #6c757d;
        }

        .recommendation-value {
            font-weight: 600;
        }

        .recommendation-description {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #dee2e6;
            font-style: italic;
            color: #495057;
        }

        /* Detailed analysis styles */
        .analysis-content {
            font-size: 0.9rem;
            line-height: 1.5;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .analysis-content h4,
        .analysis-content h5 {
            margin-top: 15px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .toggle-analysis-btn:focus {
            box-shadow: none;
        }

        /* Money Flow Styles */
        .money-flow-details {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .money-flow-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: 500;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        #moneyFlowContainer .card {
            transition: all 0.3s ease;
        }
        #moneyFlowContainer .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        #moneyFlowContainer .card-header {
            font-weight: 600;
            font-size: 0.9rem;
        }
        #moneyFlowContainer h5 {
            font-size: 1.1rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/">Options Analysis</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/options-analysis">Option Chain</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze-all">Analyze All</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/token-manager">Token Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4">Advanced Options Analysis</h1>

        <div class="card mb-4">
            <div class="card-header">
                Select Data Source
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="dataSourceSelect" class="form-label">Data Source</label>
                            <select class="form-select" id="dataSourceSelect">
                                <option value="fyers">Fyers Option Chain</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="symbolSelect" class="form-label">Symbol</label>
                            <select class="form-select" id="symbolSelect">
                                <option value="">Select a symbol</option>
                                <!-- Default index options -->
                                <option value="NSE:NIFTY50-INDEX">NSE:NIFTY50-INDEX</option>
                                <option value="NSE:FINNIFTY-INDEX">NSE:FINNIFTY-INDEX</option>
                                <option value="NSE:MIDCPNIFTY-INDEX">NSE:MIDCPNIFTY-INDEX</option>
                                <!-- Popular stock options -->
                                <option value="NSE:RELIANCE-EQ">NSE:RELIANCE-EQ</option>
                                <option value="NSE:TCS-EQ">NSE:TCS-EQ</option>
                                <option value="NSE:HDFCBANK-EQ">NSE:HDFCBANK-EQ</option>
                                <option value="NSE:INFY-EQ">NSE:INFY-EQ</option>
                                <option value="NSE:ICICIBANK-EQ">NSE:ICICIBANK-EQ</option>
                                <option value="NSE:SBIN-EQ">NSE:SBIN-EQ</option>
                                <option value="NSE:TATAMOTORS-EQ">NSE:TATAMOTORS-EQ</option>
                                <option value="NSE:ADANIENT-EQ">NSE:ADANIENT-EQ</option>
                                <option value="NSE:BAJFINANCE-EQ">NSE:BAJFINANCE-EQ</option>
                                <option value="NSE:AXISBANK-EQ">NSE:AXISBANK-EQ</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="strikeCountInput" class="form-label">Strike Count</label>
                            <input type="number" class="form-control" id="strikeCountInput" value="20" min="5" max="50">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="expirySelect" class="form-label">Expiry Date</label>
                            <select class="form-select" id="expirySelect" disabled>
                                <option value="">Select expiry date</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button class="btn btn-primary" id="fetchDataBtn">
                        <i class="bi bi-download"></i> Fetch Data
                    </button>
                </div>
            </div>
        </div>

        <div id="analysisContainer" class="d-none">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <span id="symbolTitle">Symbol</span>
                            <span class="float-end" id="timestamp">Last updated: </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1">Underlying Value</p>
                                    <h3 id="underlyingValue">0</h3>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1">Market Bias</p>
                                    <h3 id="marketBias" class="neutral">Neutral</h3>
                                    <div class="confidence-meter">
                                        <div id="confidenceValue" class="confidence-value bg-secondary" style="width: 50%;"></div>
                                    </div>
                                    <p class="small text-muted" id="confidenceText">Confidence: 50%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            Signals
                        </div>
                        <div class="card-body">
                            <div id="signalsContainer">
                                <!-- Signals will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Money Flow Analysis Section -->
            <div class="row mb-4" id="moneyFlowContainer">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <i class="bi bi-cash-stack me-2"></i>Money Flow Analysis
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">Intraday Money Flow</div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <p class="mb-1">Inflow</p>
                                                    <h5 id="moneyInflow" class="text-success">₹0 Cr</h5>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1">Outflow</p>
                                                    <h5 id="moneyOutflow" class="text-danger">₹0 Cr</h5>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <p class="mb-1">Net Flow</p>
                                                <h4 id="netMoneyFlow" class="neutral">₹0 Cr</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">FII/DII Activity</div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <p class="mb-1">FII Net</p>
                                                    <h5 id="fiiNet" class="neutral">₹0 Cr</h5>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1">DII Net</p>
                                                    <h5 id="diiNet" class="neutral">₹0 Cr</h5>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <p class="mb-1">Institutional Bias</p>
                                                <h5 id="institutionalBias" class="neutral">Neutral</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">Delivery & Futures</div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <p class="mb-1">Delivery %</p>
                                                    <h5 id="deliveryPercentage">0%</h5>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1">Futures OI Δ</p>
                                                    <h5 id="futuresOIChange" class="neutral">0%</h5>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <p class="mb-1">Money Flow Bias</p>
                                                <h5 id="moneyFlowBias" class="neutral">Neutral</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card h-100">
                                        <div class="card-header">Analysis</div>
                                        <div class="card-body">
                                            <p id="moneyFlowAnalysis" class="small">No money flow data available.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card analysis-card">
                        <div class="card-header">
                            Max Pain Analysis
                        </div>
                        <div class="card-body">
                            <p class="mb-1">Max Pain Strike</p>
                            <div class="analysis-value" id="maxPainValue">0</div>
                            <p class="mt-3" id="maxPainAnalysis"></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card analysis-card">
                        <div class="card-header">
                            Put-Call Ratio Analysis
                        </div>
                        <div class="card-body">
                            <p class="mb-1">PCR Value</p>
                            <div class="analysis-value" id="pcrValue">0</div>
                            <p class="mt-3" id="pcrAnalysis"></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card analysis-card">
                        <div class="card-header">
                            OI Change Analysis
                        </div>
                        <div class="card-body">
                            <p class="mb-1">Net OI Change</p>
                            <div class="analysis-value" id="oiChangeValue">0</div>
                            <p class="mt-3" id="oiChangeAnalysis"></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card analysis-card">
                        <div class="card-header">
                            IV Percentile Analysis
                        </div>
                        <div class="card-body">
                            <p class="mb-1">IV Percentile</p>
                            <div class="analysis-value" id="ivPercentileValue">0%</div>
                            <p class="mt-3" id="ivPercentileAnalysis"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card analysis-card">
                        <div class="card-header">
                            Gamma Exposure (GEX) Analysis
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <p class="mb-1">Net GEX</p>
                                    <div class="analysis-value" id="netGexValue">0</div>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">Call GEX</p>
                                    <div class="analysis-value" id="callGexValue">0</div>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">Put GEX</p>
                                    <div class="analysis-value" id="putGexValue">0</div>
                                </div>
                            </div>
                            <p class="mt-3" id="gexAnalysis"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            Trade Recommendations
                        </div>
                        <div class="card-body">
                            <div class="row" id="recommendationsContainer">
                                <div class="col-12 text-center" id="noRecommendationsMessage">
                                    <p>Click "Get Recommendations" to see trade suggestions based on the analysis.</p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12 text-center">
                                    <button id="getRecommendationsBtn" class="btn btn-primary">Get Recommendations</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden chart canvases for JavaScript -->
            <div class="d-none">
                <canvas id="maxPainChart"></canvas>
                <canvas id="oiDistributionChart"></canvas>
                <canvas id="gexDistributionChart"></canvas>
            </div>


        </div>

        <div id="errorMessage" class="alert alert-danger d-none"></div>

        <div id="loadingOverlay" class="loading-overlay d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM elements
            const dataSourceSelect = document.getElementById('dataSourceSelect');
            const symbolSelect = document.getElementById('symbolSelect');
            const strikeCountInput = document.getElementById('strikeCountInput');
            const expirySelect = document.getElementById('expirySelect');
            const fetchDataBtn = document.getElementById('fetchDataBtn');
            const analysisContainer = document.getElementById('analysisContainer');
            const errorMessage = document.getElementById('errorMessage');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Analysis elements
            const symbolTitle = document.getElementById('symbolTitle');
            const timestamp = document.getElementById('timestamp');
            const underlyingValue = document.getElementById('underlyingValue');
            const marketBias = document.getElementById('marketBias');
            const confidenceValue = document.getElementById('confidenceValue');
            const confidenceText = document.getElementById('confidenceText');
            const signalsContainer = document.getElementById('signalsContainer');
            const maxPainValue = document.getElementById('maxPainValue');
            const maxPainAnalysis = document.getElementById('maxPainAnalysis');
            const pcrValue = document.getElementById('pcrValue');
            const pcrAnalysis = document.getElementById('pcrAnalysis');
            const oiChangeValue = document.getElementById('oiChangeValue');
            const oiChangeAnalysis = document.getElementById('oiChangeAnalysis');
            const ivPercentileValue = document.getElementById('ivPercentileValue');
            const ivPercentileAnalysis = document.getElementById('ivPercentileAnalysis');
            const netGexValue = document.getElementById('netGexValue');
            const callGexValue = document.getElementById('callGexValue');
            const putGexValue = document.getElementById('putGexValue');
            const gexAnalysis = document.getElementById('gexAnalysis');

            // Chart contexts
            const maxPainChartElement = document.getElementById('maxPainChart');
            const oiDistributionChartElement = document.getElementById('oiDistributionChart');
            const gexDistributionChartElement = document.getElementById('gexDistributionChart');

            // Get contexts if elements exist
            const maxPainChartCtx = maxPainChartElement ? maxPainChartElement.getContext('2d') : null;
            const oiDistributionChartCtx = oiDistributionChartElement ? oiDistributionChartElement.getContext('2d') : null;
            const gexDistributionChartCtx = gexDistributionChartElement ? gexDistributionChartElement.getContext('2d') : null;

            // Chart instances
            let maxPainChart = null;
            let oiDistributionChart = null;
            let gexDistributionChart = null;

            // Current data
            let currentOptionsData = [];
            let currentUnderlyingValue = 0;

            // Load symbols based on data source
            dataSourceSelect.addEventListener('change', loadSymbols);

            // Fetch data button click
            fetchDataBtn.addEventListener('click', fetchOptionChainData);

            // Get recommendations button click
            const getRecommendationsBtn = document.getElementById('getRecommendationsBtn');
            getRecommendationsBtn.addEventListener('click', fetchTradeRecommendations);

            // Load symbols on page load
            loadSymbols();

            function loadSymbols() {
                const dataSource = dataSourceSelect.value;
                console.log('Loading symbols for data source:', dataSource);

                // We'll keep the default options that we added manually
                // and just append any additional ones from the API

                // Show loading
                showLoading();

                // Fetch symbols based on data source
                let endpoint = '';
                if (dataSource === 'fyers') {
                    endpoint = '/api/fyers-stocks';
                } else {
                    endpoint = '/api/nse-stocks';
                }

                console.log('Fetching symbols from endpoint:', endpoint);

                fetch(endpoint)
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        hideLoading();
                        console.log('Received data:', data);

                        if (data.success && data.stocks) {
                            console.log('Number of stocks:', data.stocks.length);

                            // Get current options
                            const currentOptions = Array.from(symbolSelect.options).map(opt => opt.value);

                            // Sort the stocks alphabetically for better usability
                            const sortedStocks = [...data.stocks].sort((a, b) => {
                                // Extract the stock name without the prefix for sorting
                                const nameA = a.split(':').pop().split('-')[0];
                                const nameB = b.split(':').pop().split('-')[0];
                                return nameA.localeCompare(nameB);
                            });

                            // Group stocks by type (indices first, then stocks)
                            const indices = sortedStocks.filter(s => s.includes('INDEX'));
                            const stocks = sortedStocks.filter(s => !s.includes('INDEX'));

                            // Add indices first with a group header if they're not already in the dropdown
                            let indicesAdded = false;
                            indices.forEach(symbol => {
                                if (!currentOptions.includes(symbol)) {
                                    if (!indicesAdded) {
                                        // Add a group header for indices if we're adding any
                                        const groupHeader = document.createElement('optgroup');
                                        groupHeader.label = 'Indices';
                                        symbolSelect.appendChild(groupHeader);
                                        indicesAdded = true;
                                    }
                                    const option = document.createElement('option');
                                    option.value = symbol;
                                    option.textContent = symbol;
                                    symbolSelect.appendChild(option);
                                }
                            });

                            // Add stocks with a group header if they're not already in the dropdown
                            let stocksAdded = false;
                            stocks.forEach(symbol => {
                                if (!currentOptions.includes(symbol)) {
                                    if (!stocksAdded) {
                                        // Add a group header for stocks if we're adding any
                                        const groupHeader = document.createElement('optgroup');
                                        groupHeader.label = 'Stocks';
                                        symbolSelect.appendChild(groupHeader);
                                        stocksAdded = true;
                                    }
                                    const option = document.createElement('option');
                                    option.value = symbol;
                                    option.textContent = symbol;
                                    symbolSelect.appendChild(option);
                                }
                            });

                            console.log('Symbols loaded successfully');
                        } else {
                            console.error('Failed to load symbols:', data.message);
                            // Don't show error since we have default options
                            console.log('Using default symbols only');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading symbols:', error);
                        hideLoading();
                        // Don't show error since we have default options
                        console.log('Using default symbols only due to error:', error.message);
                    });
            }

            function fetchOptionChainData() {
                const dataSource = dataSourceSelect.value;
                const symbol = symbolSelect.value;
                const strikeCount = strikeCountInput.value;
                const expiry = expirySelect.value;

                if (!symbol) {
                    showError('Please select a symbol');
                    return;
                }

                // Clear previous data
                hideError();
                analysisContainer.classList.add('d-none');

                // Show loading
                showLoading();

                // Fetch option chain data based on data source
                let endpoint = '';
                let params = '';

                if (dataSource === 'fyers') {
                    endpoint = '/api/fyers-option-chain';
                    params = `?symbol=${encodeURIComponent(symbol)}&strike_count=${strikeCount}`;
                } else {
                    endpoint = '/api/nse-option-chain';
                    params = `?symbol=${encodeURIComponent(symbol)}`;
                    if (expiry) {
                        params += `&expiry_date=${encodeURIComponent(expiry)}`;
                    }
                }

                fetch(endpoint + params)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data) {
                            // Store the data
                            currentOptionsData = data.data;
                            currentUnderlyingValue = data.underlying_value;

                            // Update expiry dates if available
                            if (data.expiry_dates && data.expiry_dates.length > 0) {
                                expirySelect.innerHTML = '<option value="">All Expiry Dates</option>';
                                data.expiry_dates.forEach(date => {
                                    const option = document.createElement('option');
                                    option.value = date;
                                    option.textContent = date;
                                    expirySelect.appendChild(option);
                                });
                                expirySelect.disabled = false;
                            }

                            // Display money flow data if available
                            if (data.money_flow) {
                                displayMoneyFlowData(data.money_flow);
                            }

                            // Perform analysis
                            performAnalysis(data.data, data.underlying_value, data.symbol, data.timestamp);
                        } else {
                            hideLoading();
                            showError(data.message || 'Failed to fetch option chain data');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showError('Error fetching option chain data: ' + error.message);
                    });
            }

            function displayMoneyFlowData(moneyFlow) {
                // Get DOM elements for money flow display
                const moneyInflow = document.getElementById('moneyInflow');
                const moneyOutflow = document.getElementById('moneyOutflow');
                const netMoneyFlow = document.getElementById('netMoneyFlow');
                const fiiNet = document.getElementById('fiiNet');
                const diiNet = document.getElementById('diiNet');
                const institutionalBias = document.getElementById('institutionalBias');
                const deliveryPercentage = document.getElementById('deliveryPercentage');
                const futuresOIChange = document.getElementById('futuresOIChange');
                const moneyFlowBias = document.getElementById('moneyFlowBias');
                const moneyFlowAnalysis = document.getElementById('moneyFlowAnalysis');

                // Show the money flow container
                document.getElementById('moneyFlowContainer').style.display = 'flex';

                if (!moneyFlow.success) {
                    // Handle error case
                    moneyFlowAnalysis.textContent = moneyFlow.message || 'Failed to fetch money flow data from Fyers API. Real-time data is required for analysis.';

                    // Create an error alert
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-3';
                    errorAlert.innerHTML = `
                        <strong>Error:</strong> ${moneyFlow.message || 'Failed to fetch real money flow data from Fyers API.'}
                        <hr>
                        <p class="mb-0">This application requires real-time data from Fyers API to calculate money flow.
                        Please check your connection, ensure your Fyers token is valid, and try again.</p>
                    `;

                    // Add the error alert to the money flow analysis card
                    const analysisCard = document.querySelector('#moneyFlowContainer .col-md-3:last-child .card-body');
                    if (analysisCard) {
                        analysisCard.innerHTML = '';
                        analysisCard.appendChild(errorAlert);
                    }

                    // Reset all values to show no data available
                    moneyInflow.textContent = 'N/A';
                    moneyOutflow.textContent = 'N/A';
                    netMoneyFlow.textContent = 'N/A';
                    netMoneyFlow.className = 'text-secondary';

                    fiiNet.textContent = 'N/A';
                    diiNet.textContent = 'N/A';
                    fiiNet.className = 'text-secondary';
                    diiNet.className = 'text-secondary';

                    institutionalBias.textContent = 'Unknown';
                    institutionalBias.className = 'text-secondary';

                    deliveryPercentage.textContent = 'N/A';
                    deliveryPercentage.className = 'text-secondary';

                    futuresOIChange.textContent = 'N/A';
                    futuresOIChange.className = 'text-secondary';

                    moneyFlowBias.textContent = 'Unknown';
                    moneyFlowBias.className = 'text-secondary';

                    return;
                }

                // Update intraday flow data
                if (moneyFlow.intraday_flow) {
                    if (moneyFlow.intraday_flow.inflow !== null && moneyFlow.intraday_flow.outflow !== null && moneyFlow.intraday_flow.net_flow !== null) {
                        moneyInflow.textContent = `₹${moneyFlow.intraday_flow.inflow} ${moneyFlow.intraday_flow.unit}`;
                        moneyOutflow.textContent = `₹${moneyFlow.intraday_flow.outflow} ${moneyFlow.intraday_flow.unit}`;

                        const netFlow = moneyFlow.intraday_flow.net_flow;
                        netMoneyFlow.textContent = `₹${Math.abs(netFlow)} ${moneyFlow.intraday_flow.unit}`;

                        // Set color based on net flow
                        if (netFlow > 0) {
                            netMoneyFlow.className = 'text-success';
                        } else if (netFlow < 0) {
                            netMoneyFlow.className = 'text-danger';
                        } else {
                            netMoneyFlow.className = 'text-secondary';
                        }
                    } else {
                        moneyInflow.textContent = 'N/A';
                        moneyOutflow.textContent = 'N/A';
                        netMoneyFlow.textContent = 'N/A';
                        netMoneyFlow.className = 'text-secondary';
                    }
                } else {
                    moneyInflow.textContent = 'N/A';
                    moneyOutflow.textContent = 'N/A';
                    netMoneyFlow.textContent = 'N/A';
                    netMoneyFlow.className = 'text-secondary';
                }

                // Update institutional activity
                if (moneyFlow.institutional_activity &&
                    moneyFlow.institutional_activity.fii &&
                    moneyFlow.institutional_activity.dii &&
                    moneyFlow.institutional_activity.fii.net !== null &&
                    moneyFlow.institutional_activity.dii.net !== null) {

                    const fiiNetValue = moneyFlow.institutional_activity.fii.net;
                    const diiNetValue = moneyFlow.institutional_activity.dii.net;

                    fiiNet.textContent = `₹${Math.abs(fiiNetValue)} ${moneyFlow.institutional_activity.fii.unit}`;
                    diiNet.textContent = `₹${Math.abs(diiNetValue)} ${moneyFlow.institutional_activity.dii.unit}`;

                    // Set color based on net values
                    fiiNet.className = fiiNetValue > 0 ? 'text-success' : (fiiNetValue < 0 ? 'text-danger' : 'text-secondary');
                    diiNet.className = diiNetValue > 0 ? 'text-success' : (diiNetValue < 0 ? 'text-danger' : 'text-secondary');

                    // Determine institutional bias
                    let instBias = 'Neutral';
                    if (fiiNetValue > 0 && diiNetValue > 0) {
                        instBias = 'Strongly Bullish';
                    } else if (fiiNetValue < 0 && diiNetValue < 0) {
                        instBias = 'Strongly Bearish';
                    } else if (fiiNetValue > 0 && Math.abs(fiiNetValue) > Math.abs(diiNetValue)) {
                        instBias = 'Moderately Bullish';
                    } else if (diiNetValue > 0 && Math.abs(diiNetValue) > Math.abs(fiiNetValue)) {
                        instBias = 'Moderately Bullish';
                    } else if (fiiNetValue < 0 && Math.abs(fiiNetValue) > Math.abs(diiNetValue)) {
                        instBias = 'Moderately Bearish';
                    } else if (diiNetValue < 0 && Math.abs(diiNetValue) > Math.abs(fiiNetValue)) {
                        instBias = 'Moderately Bearish';
                    }

                    institutionalBias.textContent = instBias;
                    institutionalBias.className = instBias.includes('Bullish') ? 'text-success' :
                                                (instBias.includes('Bearish') ? 'text-danger' : 'text-secondary');
                } else {
                    fiiNet.textContent = 'N/A';
                    diiNet.textContent = 'N/A';
                    fiiNet.className = 'text-secondary';
                    diiNet.className = 'text-secondary';
                    institutionalBias.textContent = 'Unknown';
                    institutionalBias.className = 'text-secondary';
                }

                // Update delivery percentage
                if (moneyFlow.delivery_percentage !== null && moneyFlow.delivery_percentage !== undefined) {
                    deliveryPercentage.textContent = `${moneyFlow.delivery_percentage.toFixed(1)}%`;

                    // Set color based on delivery percentage
                    if (moneyFlow.delivery_percentage > 60) {
                        deliveryPercentage.className = 'text-success';
                    } else if (moneyFlow.delivery_percentage < 40) {
                        deliveryPercentage.className = 'text-danger';
                    } else {
                        deliveryPercentage.className = 'text-secondary';
                    }
                } else {
                    deliveryPercentage.textContent = 'N/A';
                    deliveryPercentage.className = 'text-secondary';
                }

                // Update futures OI change
                if (moneyFlow.futures_oi && moneyFlow.futures_oi.change_percent !== null && moneyFlow.futures_oi.change_percent !== undefined) {
                    futuresOIChange.textContent = `${moneyFlow.futures_oi.change_percent.toFixed(1)}%`;

                    // Set color based on OI change
                    if (moneyFlow.futures_oi.change_percent > 2) {
                        futuresOIChange.className = 'text-success';
                    } else if (moneyFlow.futures_oi.change_percent < -2) {
                        futuresOIChange.className = 'text-danger';
                    } else {
                        futuresOIChange.className = 'text-secondary';
                    }
                } else {
                    futuresOIChange.textContent = 'N/A';
                    futuresOIChange.className = 'text-secondary';
                }

                // Update money flow bias
                if (moneyFlow.flow_bias && moneyFlow.flow_bias !== 'Unknown') {
                    moneyFlowBias.textContent = moneyFlow.flow_bias;
                    if (moneyFlow.flow_strength) {
                        moneyFlowBias.textContent += ` (${moneyFlow.flow_strength})`;
                    }

                    // Set color based on bias
                    if (moneyFlow.flow_bias === 'Bullish') {
                        moneyFlowBias.className = 'text-success';
                    } else if (moneyFlow.flow_bias === 'Bearish') {
                        moneyFlowBias.className = 'text-danger';
                    } else {
                        moneyFlowBias.className = 'text-secondary';
                    }
                } else {
                    moneyFlowBias.textContent = 'Unknown';
                    moneyFlowBias.className = 'text-secondary';
                }

                // Update analysis text
                if (moneyFlow.analysis) {
                    moneyFlowAnalysis.textContent = moneyFlow.analysis;

                    // Add a badge to indicate real data is being used
                    const realDataBadge = document.createElement('div');
                    realDataBadge.className = 'badge bg-success mt-2';
                    realDataBadge.textContent = 'Using Real-Time Fyers Data';
                    moneyFlowAnalysis.parentNode.appendChild(realDataBadge);
                } else {
                    moneyFlowAnalysis.textContent = 'No detailed analysis available. Real-time data from Fyers API is required for analysis.';
                }
            }

            function performAnalysis(optionsData, underlyingValue, symbol, dataTimestamp) {
                // Get selected expiry date
                const expiryDate = expirySelect.value;

                // Call the analysis API
                fetch('/api/options-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        options_data: optionsData,
                        current_price: underlyingValue,
                        days_to_expiry: expiryDate
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();

                        if (data.success && data.analysis) {
                            // Display the analysis results
                            displayAnalysisResults(data.analysis, symbol, dataTimestamp, underlyingValue);
                        } else {
                            showError(data.message || 'Failed to perform options analysis');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showError('Error performing options analysis: ' + error.message);
                    });
            }

            function displayAnalysisResults(analysis, symbol, dataTimestamp, underlyingVal) {
                // Update symbol and timestamp
                symbolTitle.textContent = symbol;
                timestamp.textContent = `Last updated: ${dataTimestamp || new Date().toLocaleString()}`;
                underlyingValue.textContent = underlyingVal.toLocaleString();

                // Update market bias
                marketBias.textContent = analysis.overall_bias;
                marketBias.className = analysis.overall_bias.toLowerCase();

                // Update confidence
                confidenceValue.style.width = `${analysis.confidence}%`;
                confidenceText.textContent = `Confidence: ${analysis.confidence.toFixed(1)}%`;

                // Set confidence color based on bias
                if (analysis.overall_bias === 'Bullish') {
                    confidenceValue.className = 'confidence-value bg-success';
                } else if (analysis.overall_bias === 'Bearish') {
                    confidenceValue.className = 'confidence-value bg-danger';
                } else {
                    confidenceValue.className = 'confidence-value bg-secondary';
                }

                // Update signals
                signalsContainer.innerHTML = '';
                analysis.signals.forEach(signal => {
                    const badge = document.createElement('span');
                    badge.className = `signal-badge badge ${getBiasClass(signal.bias)}`;
                    badge.textContent = `${signal.name}: ${signal.bias}`;
                    signalsContainer.appendChild(badge);
                });

                // Update Max Pain
                maxPainValue.textContent = analysis.max_pain.max_pain_strike.toLocaleString();
                maxPainAnalysis.textContent = analysis.max_pain.analysis;

                // Update PCR
                pcrValue.textContent = analysis.pcr.pcr.toFixed(2);
                pcrAnalysis.textContent = analysis.pcr.analysis;

                // Update OI Change
                oiChangeValue.textContent = analysis.oi_change.net_oi_change.toLocaleString();
                oiChangeAnalysis.textContent = analysis.oi_change.analysis;

                // Update IV Percentile
                ivPercentileValue.textContent = `${analysis.iv_percentile.iv_percentile.toFixed(1)}%`;
                ivPercentileAnalysis.textContent = analysis.iv_percentile.analysis;

                // Update GEX
                netGexValue.textContent = analysis.gex.net_gex.toLocaleString();
                callGexValue.textContent = analysis.gex.call_gex.toLocaleString();
                putGexValue.textContent = analysis.gex.put_gex.toLocaleString();
                gexAnalysis.textContent = analysis.gex.analysis;

                // Set GEX colors based on values
                if (analysis.gex.net_gex > 0) {
                    netGexValue.className = 'analysis-value bullish';
                } else if (analysis.gex.net_gex < 0) {
                    netGexValue.className = 'analysis-value bearish';
                } else {
                    netGexValue.className = 'analysis-value neutral';
                }

                callGexValue.className = 'analysis-value bullish';
                putGexValue.className = 'analysis-value bearish';

                // Create Max Pain Chart
                createMaxPainChart(analysis.max_pain.pain_by_strike, analysis.max_pain.max_pain_strike, underlyingVal);

                // Create OI Distribution Chart
                createOIDistributionChart(analysis.max_pain.pain_by_strike, underlyingVal);

                // Create GEX Distribution Chart
                createGEXDistributionChart(analysis.gex.gex_by_strike, underlyingVal);

                // Show analysis container
                analysisContainer.classList.remove('d-none');
            }

            function createMaxPainChart(painByStrike, maxPainStrike, underlyingValue) {
                // Skip if chart context doesn't exist
                if (!maxPainChartCtx) {
                    console.log('Max Pain chart context not available, skipping chart creation');
                    return;
                }

                // Destroy previous chart if exists
                if (maxPainChart) {
                    maxPainChart.destroy();
                }

                // Prepare data
                const strikes = painByStrike.map(item => item.strike);
                const painValues = painByStrike.map(item => item.pain);

                // Create chart
                maxPainChart = new Chart(maxPainChartCtx, {
                    type: 'line',
                    data: {
                        labels: strikes,
                        datasets: [{
                            label: 'Pain',
                            data: painValues,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderWidth: 2,
                            pointRadius: 3,
                            fill: true,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Pain Value'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Strike Price'
                                }
                            }
                        },
                        plugins: {
                            annotation: {
                                annotations: {
                                    maxPainLine: {
                                        type: 'line',
                                        xMin: maxPainStrike,
                                        xMax: maxPainStrike,
                                        borderColor: 'rgba(255, 99, 132, 1)',
                                        borderWidth: 2,
                                        label: {
                                            content: 'Max Pain',
                                            enabled: true,
                                            position: 'top'
                                        }
                                    },
                                    currentPriceLine: {
                                        type: 'line',
                                        xMin: underlyingValue,
                                        xMax: underlyingValue,
                                        borderColor: 'rgba(54, 162, 235, 1)',
                                        borderWidth: 2,
                                        borderDash: [5, 5],
                                        label: {
                                            content: 'Current Price',
                                            enabled: true,
                                            position: 'bottom'
                                        }
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `Pain: ${context.parsed.y.toLocaleString()}`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            function createOIDistributionChart(painByStrike, underlyingValue) {
                // Skip if chart context doesn't exist
                if (!oiDistributionChartCtx) {
                    console.log('OI Distribution chart context not available, skipping chart creation');
                    return;
                }

                // Destroy previous chart if exists
                if (oiDistributionChart) {
                    oiDistributionChart.destroy();
                }

                // Prepare data
                const strikes = painByStrike.map(item => item.strike);
                const callOI = painByStrike.map(item => item.call_oi);
                const putOI = painByStrike.map(item => item.put_oi);

                // Create chart
                oiDistributionChart = new Chart(oiDistributionChartCtx, {
                    type: 'bar',
                    data: {
                        labels: strikes,
                        datasets: [
                            {
                                label: 'Call OI',
                                data: callOI,
                                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Put OI',
                                data: putOI,
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Open Interest'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Strike Price'
                                }
                            }
                        },
                        plugins: {
                            annotation: {
                                annotations: {
                                    currentPriceLine: {
                                        type: 'line',
                                        xMin: underlyingValue,
                                        xMax: underlyingValue,
                                        borderColor: 'rgba(75, 192, 192, 1)',
                                        borderWidth: 2,
                                        label: {
                                            content: 'Current Price',
                                            enabled: true,
                                            position: 'top'
                                        }
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        return `${label}: ${context.parsed.y.toLocaleString()}`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            function createGEXDistributionChart(gexByStrike, underlyingValue) {
                // Skip if chart context doesn't exist
                if (!gexDistributionChartCtx) {
                    console.log('GEX Distribution chart context not available, skipping chart creation');
                    return;
                }

                // Destroy previous chart if exists
                if (gexDistributionChart) {
                    gexDistributionChart.destroy();
                }

                // Prepare data
                const strikes = gexByStrike.map(item => item.strike);
                const gexValues = gexByStrike.map(item => item.gex);

                // Create positive and negative datasets for better visualization
                const positiveGex = gexValues.map(value => value > 0 ? value : 0);
                const negativeGex = gexValues.map(value => value < 0 ? value : 0);

                // Create chart
                gexDistributionChart = new Chart(gexDistributionChartCtx, {
                    type: 'bar',
                    data: {
                        labels: strikes,
                        datasets: [
                            {
                                label: 'Positive GEX',
                                data: positiveGex,
                                backgroundColor: 'rgba(40, 167, 69, 0.5)',
                                borderColor: 'rgba(40, 167, 69, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Negative GEX',
                                data: negativeGex,
                                backgroundColor: 'rgba(220, 53, 69, 0.5)',
                                borderColor: 'rgba(220, 53, 69, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: 'Gamma Exposure'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Strike Price'
                                }
                            }
                        },
                        plugins: {
                            annotation: {
                                annotations: {
                                    currentPriceLine: {
                                        type: 'line',
                                        xMin: underlyingValue,
                                        xMax: underlyingValue,
                                        borderColor: 'rgba(75, 192, 192, 1)',
                                        borderWidth: 2,
                                        label: {
                                            content: 'Current Price',
                                            enabled: true,
                                            position: 'top'
                                        }
                                    },
                                    zeroLine: {
                                        type: 'line',
                                        yMin: 0,
                                        yMax: 0,
                                        borderColor: 'rgba(128, 128, 128, 0.5)',
                                        borderWidth: 1,
                                        borderDash: [5, 5]
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        return `${label}: ${context.parsed.y.toLocaleString()}`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Trade recommendation functions
            function fetchTradeRecommendations() {
                console.log('Fetching trade recommendations...');

                // Check if we have option data
                if (!currentOptionsData || !currentOptionsData.length || !currentUnderlyingValue) {
                    console.error('No option data available');
                    showError('Please fetch option chain data first');
                    return;
                }

                console.log('Option data available:', currentOptionsData.length, 'items');
                console.log('Current price:', currentUnderlyingValue);

                // Get selected symbol
                const symbol = symbolSelect.value;
                console.log('Selected symbol:', symbol);

                // Get selected expiry date
                const expiryDate = expirySelect.value;
                console.log('Selected expiry date:', expiryDate);

                // Get money flow data if available
                let moneyFlowData = null;
                const moneyFlowContainer = document.getElementById('moneyFlowContainer');
                if (moneyFlowContainer && window.getComputedStyle(moneyFlowContainer).display !== 'none') {
                    console.log('Money flow container is visible, extracting data...');

                    try {
                        // Extract money flow data from the UI
                        const netFlow = document.getElementById('netMoneyFlow').textContent;
                        const fiiNet = document.getElementById('fiiNet').textContent;
                        const diiNet = document.getElementById('diiNet').textContent;
                        const deliveryPercentage = document.getElementById('deliveryPercentage').textContent;
                        const futuresOIChange = document.getElementById('futuresOIChange').textContent;
                        const moneyFlowBias = document.getElementById('moneyFlowBias').textContent;
                        const moneyFlowAnalysis = document.getElementById('moneyFlowAnalysis').textContent;

                        console.log('Extracted money flow data:', {
                            netFlow, fiiNet, diiNet, deliveryPercentage,
                            futuresOIChange, moneyFlowBias, moneyFlowAnalysis
                        });

                        // Create money flow data object
                        moneyFlowData = {
                            success: true,
                            flow_bias: moneyFlowBias.split(' ')[0], // Extract just the bias part
                            flow_strength: moneyFlowBias.includes('(') ? moneyFlowBias.split('(')[1].replace(')', '') : 'Moderate',
                            analysis: moneyFlowAnalysis,
                            intraday_flow: {
                                net_flow: parseFloat(netFlow.replace(/[^0-9.-]+/g, "") || "0")
                            },
                            institutional_activity: {
                                fii: {
                                    net: parseFloat(fiiNet.replace(/[^0-9.-]+/g, "") || "0")
                                },
                                dii: {
                                    net: parseFloat(diiNet.replace(/[^0-9.-]+/g, "") || "0")
                                }
                            },
                            delivery_percentage: parseFloat(deliveryPercentage.replace(/[^0-9.-]+/g, "") || "0"),
                            futures_oi: {
                                change_percent: parseFloat(futuresOIChange.replace(/[^0-9.-]+/g, "") || "0")
                            }
                        };

                        console.log('Created money flow data object:', moneyFlowData);
                    } catch (error) {
                        console.error('Error extracting money flow data:', error);
                        moneyFlowData = null;
                    }
                } else {
                    console.log('Money flow container is not visible, skipping money flow data');
                }

                // Show loading
                showLoading();
                console.log('Showing loading indicator');

                // Clear previous recommendations
                const recommendationsContainer = document.getElementById('recommendationsContainer');
                const noRecommendationsMessage = document.getElementById('noRecommendationsMessage');

                if (recommendationsContainer && noRecommendationsMessage) {
                    console.log('Clearing previous recommendations');
                    recommendationsContainer.innerHTML = '';
                    recommendationsContainer.appendChild(noRecommendationsMessage);
                } else {
                    console.error('Recommendations container or message element not found');
                }

                // Prepare request data
                const requestData = {
                    options_data: currentOptionsData,
                    current_price: currentUnderlyingValue,
                    symbol: symbol,
                    days_to_expiry: expiryDate,
                    include_institutional: false,  // Disable institutional analysis integration
                    money_flow: moneyFlowData     // Include money flow data if available
                };

                console.log('Sending request to /api/trade-recommendations with data:', JSON.stringify(requestData).substring(0, 200) + '...');

                // Call the trade recommendations API with institutional analysis integration
                fetch('/api/trade-recommendations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    console.log('Received response with status:', response.status);
                    return response.json();
                })
                .then(data => {
                    hideLoading();
                    console.log('Received data:', data);

                    if (data.success && data.recommendations) {
                        console.log('Displaying trade recommendations');
                        displayTradeRecommendations(data.recommendations, data.institutional_analysis_included, data.money_flow_enhanced);
                    } else {
                        console.error('Failed to generate recommendations:', data.message);
                        showError(data.message || 'Failed to generate trade recommendations');
                    }
                })
                .catch(error => {
                    console.error('Error in fetch operation:', error);
                    hideLoading();
                    showError('Error generating trade recommendations: ' + error.message);
                });
            }

            function displayTradeRecommendations(recommendations, institutionalAnalysisIncluded, moneyFlowEnhanced) {
                console.log('Displaying trade recommendations:', recommendations);

                const recommendationsContainer = document.getElementById('recommendationsContainer');
                const noRecommendationsMessage = document.getElementById('noRecommendationsMessage');

                if (!recommendationsContainer) {
                    console.error('Recommendations container not found');
                    alert('Error: Recommendations container not found');
                    return;
                }

                if (!noRecommendationsMessage) {
                    console.error('No recommendations message element not found');
                }

                // Clear previous recommendations
                console.log('Clearing previous recommendations');
                recommendationsContainer.innerHTML = '';

                // Add institutional analysis badge if included
                if (institutionalAnalysisIncluded) {
                    console.log('Adding institutional analysis badge');
                    const institutionalBadge = document.createElement('div');
                    institutionalBadge.className = 'col-12 mb-3';
                    institutionalBadge.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle-fill me-2"></i>Recommendations enhanced with institutional-level analysis</div>';
                    recommendationsContainer.appendChild(institutionalBadge);
                }

                // Add money flow badge if included
                if (moneyFlowEnhanced) {
                    console.log('Adding money flow badge');
                    const moneyFlowBadge = document.createElement('div');
                    moneyFlowBadge.className = 'col-12 mb-3';
                    moneyFlowBadge.innerHTML = '<div class="alert alert-success"><i class="bi bi-cash-stack me-2"></i>Recommendations enhanced with money flow analysis</div>';
                    recommendationsContainer.appendChild(moneyFlowBadge);

                    // Add money flow analysis summary if available
                    if (recommendations.money_flow_analysis) {
                        console.log('Adding money flow analysis summary');
                        const moneyFlowAnalysis = document.createElement('div');
                        moneyFlowAnalysis.className = 'col-12 mb-3';
                        moneyFlowAnalysis.innerHTML = `<div class="alert alert-light border"><strong>Money Flow Analysis:</strong> ${recommendations.money_flow_analysis}</div>`;
                        recommendationsContainer.appendChild(moneyFlowAnalysis);
                    }
                }

                // Check if we have the best trade recommendation
                const hasBestTrade = recommendations.best_trade && Object.keys(recommendations.best_trade).length > 0;
                console.log('Has best trade recommendation:', hasBestTrade);

                // Check if we have any recommendations
                const hasCallRecommendations = recommendations.calls && recommendations.calls.length > 0;
                const hasPutRecommendations = recommendations.puts && recommendations.puts.length > 0;
                console.log('Has call recommendations:', hasCallRecommendations, 'Has put recommendations:', hasPutRecommendations);

                if (!hasBestTrade && !hasCallRecommendations && !hasPutRecommendations) {
                    console.log('No recommendations available, showing no recommendations message');
                    if (noRecommendationsMessage) {
                        noRecommendationsMessage.classList.remove('d-none');
                        recommendationsContainer.appendChild(noRecommendationsMessage);
                    } else {
                        const noRecsMessage = document.createElement('div');
                        noRecsMessage.className = 'col-12 text-center';
                        noRecsMessage.innerHTML = '<p>No trade recommendations available based on current data.</p>';
                        recommendationsContainer.appendChild(noRecsMessage);
                    }
                    return;
                }

                // Hide no recommendations message
                if (noRecommendationsMessage) {
                    console.log('Hiding no recommendations message');
                    noRecommendationsMessage.classList.add('d-none');
                }

                // If we have a best trade recommendation, display it prominently
                if (hasBestTrade) {
                    console.log('Creating best trade recommendation card');
                    const bestTradeCol = document.createElement('div');
                    bestTradeCol.className = 'col-12 mb-4';

                    const bestTradeHeader = document.createElement('h4');
                    bestTradeHeader.className = 'mb-3';
                    bestTradeHeader.innerHTML = 'Best Trade Recommendation <span class="badge bg-success">Focused Analysis</span>';
                    bestTradeCol.appendChild(bestTradeHeader);

                    try {
                        const bestTradeCard = createRecommendationCard(
                            recommendations.best_trade,
                            recommendations.best_trade.option_type
                        );
                        bestTradeCol.appendChild(bestTradeCard);
                    } catch (error) {
                        console.error('Error creating best trade card:', error);
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'alert alert-danger';
                        errorMsg.textContent = 'Error creating recommendation card: ' + error.message;
                        bestTradeCol.appendChild(errorMsg);
                    }

                    recommendationsContainer.appendChild(bestTradeCol);

                    // Add a divider if we have other recommendations
                    if (hasCallRecommendations || hasPutRecommendations) {
                        console.log('Adding divider for alternative recommendations');
                        const divider = document.createElement('div');
                        divider.className = 'col-12 mb-4';
                        divider.innerHTML = '<hr><h5 class="text-muted mb-3">Alternative Recommendations</h5>';
                        recommendationsContainer.appendChild(divider);
                    }
                }

                // Create call recommendations
                if (hasCallRecommendations) {
                    console.log('Creating call recommendations');
                    const callsCol = document.createElement('div');
                    callsCol.className = hasPutRecommendations ? 'col-md-6' : 'col-12';

                    recommendations.calls.forEach((rec, index) => {
                        console.log(`Creating call recommendation card ${index + 1}`);
                        try {
                            const card = createRecommendationCard(rec, 'CALL');
                            callsCol.appendChild(card);
                        } catch (error) {
                            console.error(`Error creating call card ${index + 1}:`, error);
                            const errorMsg = document.createElement('div');
                            errorMsg.className = 'alert alert-danger';
                            errorMsg.textContent = `Error creating call recommendation ${index + 1}: ${error.message}`;
                            callsCol.appendChild(errorMsg);
                        }
                    });

                    recommendationsContainer.appendChild(callsCol);
                }

                // Create put recommendations
                if (hasPutRecommendations) {
                    console.log('Creating put recommendations');
                    const putsCol = document.createElement('div');
                    putsCol.className = hasCallRecommendations ? 'col-md-6' : 'col-12';

                    recommendations.puts.forEach((rec, index) => {
                        console.log(`Creating put recommendation card ${index + 1}`);
                        try {
                            const card = createRecommendationCard(rec, 'PUT');
                            putsCol.appendChild(card);
                        } catch (error) {
                            console.error(`Error creating put card ${index + 1}:`, error);
                            const errorMsg = document.createElement('div');
                            errorMsg.className = 'alert alert-danger';
                            errorMsg.textContent = `Error creating put recommendation ${index + 1}: ${error.message}`;
                            putsCol.appendChild(errorMsg);
                        }
                    });

                    recommendationsContainer.appendChild(putsCol);
                }

                console.log('Finished displaying trade recommendations');
            }

            function createRecommendationCard(recommendation, type) {
                console.log('Creating recommendation card for', type, 'at strike', recommendation.strike);

                if (!recommendation) {
                    console.error('Recommendation object is null or undefined');
                    throw new Error('Recommendation data is missing');
                }

                const card = document.createElement('div');
                card.className = 'recommendation-card';

                try {
                    // Create header
                    const header = document.createElement('div');
                    header.className = `recommendation-header ${type.toLowerCase()}-header`;

                    // Include moneyness in the title if available
                    const moneyness = recommendation.moneyness ? ` (${recommendation.moneyness})` : '';

                    const title = document.createElement('div');
                    title.textContent = `${recommendation.symbol || 'Unknown'} ${type} @ ${recommendation.strike || 'N/A'}${moneyness}`;

                    const score = document.createElement('div');
                    score.className = 'badge bg-primary';

                    // Use enhanced score if available, otherwise use original score
                    if (recommendation.enhanced_score !== undefined) {
                        score.textContent = `Score: ${Math.round(recommendation.enhanced_score)}/100`;
                    } else {
                        score.textContent = `Score: ${recommendation.score || 0}/10`;
                    }

                    header.appendChild(title);
                    header.appendChild(score);
                    card.appendChild(header);

                    // Create body
                    const body = document.createElement('div');
                    body.className = 'recommendation-body';

                    // Add price info
                    addRecommendationRow(body, 'Buy Price:', `₹${recommendation.buy_price !== null && recommendation.buy_price !== undefined ? recommendation.buy_price.toFixed(2) : 'N/A'}`);
                    addRecommendationRow(body, 'Exit Target:', `₹${recommendation.exit_target !== null && recommendation.exit_target !== undefined ? recommendation.exit_target.toFixed(2) : 'N/A'}`);
                    addRecommendationRow(body, 'Stop Loss:', `₹${recommendation.stop_loss !== null && recommendation.stop_loss !== undefined ? recommendation.stop_loss.toFixed(2) : 'N/A'}`);

                    // Add risk-reward ratio
                    addRecommendationRow(body, 'Risk:Reward:', `1:${recommendation.risk_reward !== null && recommendation.risk_reward !== undefined ? recommendation.risk_reward.toFixed(2) : 'N/A'}`);

                    // Add additional info
                    addRecommendationRow(body, 'IV:', `${recommendation.iv !== null && recommendation.iv !== undefined ? recommendation.iv.toFixed(2) : 'N/A'}%`);

                    // Format large numbers with commas
                    const formattedOiChange = recommendation.oi_change !== null && recommendation.oi_change !== undefined ? recommendation.oi_change.toLocaleString() : 'N/A';
                    const formattedVolume = recommendation.volume !== null && recommendation.volume !== undefined ? recommendation.volume.toLocaleString() : 'N/A';

                    addRecommendationRow(body, 'OI Change:', formattedOiChange);
                    addRecommendationRow(body, 'Volume:', formattedVolume);

                    if (recommendation.change_pct !== null && recommendation.change_pct !== undefined) {
                        addRecommendationRow(body, 'Price Change:', `${recommendation.change_pct.toFixed(2)}%`);
                    }

                    addRecommendationRow(body, 'ATR:', recommendation.atr || 'N/A');

                    // Add description
                    if (recommendation.description) {
                        const description = document.createElement('div');
                        description.className = 'recommendation-description';
                        description.textContent = recommendation.description;
                        body.appendChild(description);
                    }

                    // Add money flow information if available
                    if (recommendation.money_flow) {
                        console.log('Adding money flow information to card');
                        try {
                            const moneyFlowContainer = document.createElement('div');
                            moneyFlowContainer.className = 'mt-3 pt-3 border-top';

                            const moneyFlowHeader = document.createElement('h5');
                            moneyFlowHeader.className = 'mb-2';
                            moneyFlowHeader.textContent = 'Money Flow Insights';

                            moneyFlowContainer.appendChild(moneyFlowHeader);

                            // Create money flow badge
                            const moneyFlowBadge = document.createElement('div');
                            const flowBias = recommendation.money_flow.bias || 'Neutral';
                            const flowStrength = recommendation.money_flow.strength || 'Moderate';

                            let badgeClass = 'badge ';
                            if (flowBias === 'Bullish') {
                                badgeClass += 'bg-success';
                            } else if (flowBias === 'Bearish') {
                                badgeClass += 'bg-danger';
                            } else {
                                badgeClass += 'bg-secondary';
                            }

                            moneyFlowBadge.className = badgeClass + ' mb-2';
                            moneyFlowBadge.textContent = `${flowBias} (${flowStrength})`;
                            moneyFlowContainer.appendChild(moneyFlowBadge);

                            // Add alignment indicator
                            if (recommendation.money_flow.alignment) {
                                const alignmentBadge = document.createElement('span');
                                alignmentBadge.className = 'badge bg-info ms-2';
                                alignmentBadge.textContent = 'Aligned with Options';
                                moneyFlowContainer.appendChild(alignmentBadge);
                            }

                            // Add money flow details
                            const moneyFlowDetails = document.createElement('div');
                            moneyFlowDetails.className = 'money-flow-details small mt-2';

                            // Add FII/DII info
                            const fiiNet = recommendation.money_flow.fii_net;
                            const diiNet = recommendation.money_flow.dii_net;

                            if (fiiNet !== undefined) {
                                const fiiInfo = document.createElement('div');
                                fiiInfo.className = 'mb-1';
                                fiiInfo.innerHTML = `FII Net: <span class="${fiiNet > 0 ? 'text-success' : (fiiNet < 0 ? 'text-danger' : 'text-secondary')}">₹${Math.abs(fiiNet)} Cr</span>`;
                                moneyFlowDetails.appendChild(fiiInfo);
                            }

                            if (diiNet !== undefined) {
                                const diiInfo = document.createElement('div');
                                diiInfo.className = 'mb-1';
                                diiInfo.innerHTML = `DII Net: <span class="${diiNet > 0 ? 'text-success' : (diiNet < 0 ? 'text-danger' : 'text-secondary')}">₹${Math.abs(diiNet)} Cr</span>`;
                                moneyFlowDetails.appendChild(diiInfo);
                            }

                            // Add delivery percentage if available
                            const deliveryPct = recommendation.money_flow.delivery_percentage;
                            if (deliveryPct !== undefined && deliveryPct !== null) {
                                const deliveryInfo = document.createElement('div');
                                deliveryInfo.className = 'mb-1';
                                let deliveryClass = 'text-secondary';
                                if (deliveryPct > 60) deliveryClass = 'text-success';
                                else if (deliveryPct < 40) deliveryClass = 'text-danger';

                                deliveryInfo.innerHTML = `Delivery %: <span class="${deliveryClass}">${deliveryPct ? deliveryPct.toFixed(1) : 'N/A'}%</span>`;
                                moneyFlowDetails.appendChild(deliveryInfo);
                            }

                            // Add futures OI change if available
                            const futuresOIChange = recommendation.money_flow.futures_oi_change;
                            if (futuresOIChange !== undefined) {
                                const futuresInfo = document.createElement('div');
                                futuresInfo.className = 'mb-1';
                                let futuresClass = 'text-secondary';
                                if (futuresOIChange > 2) futuresClass = 'text-success';
                                else if (futuresOIChange < -2) futuresClass = 'text-danger';

                                futuresInfo.innerHTML = `Futures OI Change: <span class="${futuresClass}">${futuresOIChange ? futuresOIChange.toFixed(1) : 'N/A'}%</span>`;
                                moneyFlowDetails.appendChild(futuresInfo);
                            }

                            moneyFlowContainer.appendChild(moneyFlowDetails);
                            body.appendChild(moneyFlowContainer);
                        } catch (error) {
                            console.error('Error adding money flow information:', error);
                            const errorMsg = document.createElement('div');
                            errorMsg.className = 'alert alert-warning mt-3';
                            errorMsg.textContent = 'Error displaying money flow data';
                            body.appendChild(errorMsg);
                        }
                    }

                    // Add enhanced analysis if available
                    if (recommendation.enhanced_analysis) {
                        console.log('Adding enhanced analysis to card');
                        try {
                            const enhancedAnalysisContainer = document.createElement('div');
                            enhancedAnalysisContainer.className = 'mt-3 pt-3 border-top';

                            const enhancedAnalysisHeader = document.createElement('h5');
                            enhancedAnalysisHeader.className = 'mb-2';
                            enhancedAnalysisHeader.textContent = 'Enhanced Analysis';

                            const enhancedAnalysisContent = document.createElement('div');
                            enhancedAnalysisContent.className = 'enhanced-analysis small';
                            enhancedAnalysisContent.style.whiteSpace = 'pre-line';
                            enhancedAnalysisContent.textContent = recommendation.enhanced_analysis;

                            enhancedAnalysisContainer.appendChild(enhancedAnalysisHeader);
                            enhancedAnalysisContainer.appendChild(enhancedAnalysisContent);
                            body.appendChild(enhancedAnalysisContainer);
                        } catch (error) {
                            console.error('Error adding enhanced analysis:', error);
                        }
                    }

                    // Add score breakdown if available
                    if (recommendation.score_breakdown) {
                        console.log('Adding score breakdown to card');
                        try {
                            const scoreBreakdownContainer = document.createElement('div');
                            scoreBreakdownContainer.className = 'mt-3 pt-3 border-top';

                            const scoreBreakdownHeader = document.createElement('h5');
                            scoreBreakdownHeader.className = 'mb-2';
                            scoreBreakdownHeader.textContent = 'Score Breakdown';

                            scoreBreakdownContainer.appendChild(scoreBreakdownHeader);

                            // Create score breakdown table
                            const scoreTable = document.createElement('table');
                            scoreTable.className = 'table table-sm table-bordered';

                            const tableHead = document.createElement('thead');
                            tableHead.innerHTML = `
                                <tr>
                                    <th>Factor</th>
                                    <th>Score</th>
                                    <th>Weight</th>
                                </tr>
                            `;

                            const tableBody = document.createElement('tbody');

                            // Add rows for each factor
                            const breakdown = recommendation.score_breakdown;

                            // Money Flow
                            if (breakdown.money_flow_score !== undefined) {
                                const moneyFlowRow = document.createElement('tr');
                                moneyFlowRow.innerHTML = `
                                    <td>Money Flow</td>
                                    <td>${breakdown.money_flow_score.toFixed(1)}/10</td>
                                    <td>20%</td>
                                `;
                                tableBody.appendChild(moneyFlowRow);
                            }

                            // OI Analysis
                            if (breakdown.oi_score !== undefined) {
                                const oiRow = document.createElement('tr');
                                oiRow.innerHTML = `
                                    <td>OI Analysis</td>
                                    <td>${breakdown.oi_score.toFixed(1)}/10</td>
                                    <td>30%</td>
                                `;
                                tableBody.appendChild(oiRow);
                            }

                            // FII/DII Activity
                            if (breakdown.fii_dii_score !== undefined) {
                                const fiiDiiRow = document.createElement('tr');
                                fiiDiiRow.innerHTML = `
                                    <td>FII/DII Activity</td>
                                    <td>${breakdown.fii_dii_score.toFixed(1)}/10</td>
                                    <td>20%</td>
                                `;
                                tableBody.appendChild(fiiDiiRow);
                            }

                            // Technical Trend
                            if (breakdown.trend_score !== undefined) {
                                const trendRow = document.createElement('tr');
                                trendRow.innerHTML = `
                                    <td>Technical Trend</td>
                                    <td>${breakdown.trend_score.toFixed(1)}/10</td>
                                    <td>30%</td>
                                `;
                                tableBody.appendChild(trendRow);
                            }

                            // Volume Factor
                            if (breakdown.volume_score !== undefined) {
                                const volumeRow = document.createElement('tr');
                                volumeRow.innerHTML = `
                                    <td>Volume Factor</td>
                                    <td>${breakdown.volume_score.toFixed(1)}/10</td>
                                    <td>Adjustment</td>
                                `;
                                tableBody.appendChild(volumeRow);
                            }

                            // Expiry Factor
                            if (breakdown.expiry_score !== undefined) {
                                const expiryRow = document.createElement('tr');
                                expiryRow.innerHTML = `
                                    <td>Expiry Factor</td>
                                    <td>${breakdown.expiry_score.toFixed(1)}/10</td>
                                    <td>Adjustment</td>
                                `;
                                tableBody.appendChild(expiryRow);
                            }

                            // News Factor
                            if (breakdown.news_score !== undefined) {
                                const newsRow = document.createElement('tr');
                                newsRow.innerHTML = `
                                    <td>News Sensitivity</td>
                                    <td>${breakdown.news_score.toFixed(1)}/10</td>
                                    <td>Adjustment</td>
                                `;
                                tableBody.appendChild(newsRow);
                            }

                            // Final Score
                            if (breakdown.final_score !== undefined) {
                                const finalRow = document.createElement('tr');
                                finalRow.className = 'table-primary font-weight-bold';
                                finalRow.innerHTML = `
                                    <td>Final Score</td>
                                    <td colspan="2">${breakdown.final_score.toFixed(1)}/100</td>
                                `;
                                tableBody.appendChild(finalRow);
                            }

                            scoreTable.appendChild(tableHead);
                            scoreTable.appendChild(tableBody);
                            scoreBreakdownContainer.appendChild(scoreTable);

                            body.appendChild(scoreBreakdownContainer);
                        } catch (error) {
                            console.error('Error adding score breakdown:', error);
                        }
                    }

                    // Add detailed analysis if available
                    if (recommendation.detailed_analysis) {
                        console.log('Adding detailed analysis to card');
                        try {
                            const analysisContainer = document.createElement('div');
                            analysisContainer.className = 'mt-4 pt-3 border-top';

                            const analysisHeader = document.createElement('div');
                            analysisHeader.className = 'd-flex justify-content-between align-items-center mb-2';

                            const analysisTitle = document.createElement('h5');
                            analysisTitle.className = 'mb-0';
                            analysisTitle.textContent = 'Detailed Analysis';

                            const toggleButton = document.createElement('button');
                            toggleButton.className = 'btn btn-sm btn-outline-primary';
                            toggleButton.textContent = 'Show';

                            analysisHeader.appendChild(analysisTitle);
                            analysisHeader.appendChild(toggleButton);

                            const analysisContent = document.createElement('div');
                            analysisContent.className = 'analysis-content';
                            analysisContent.style.display = 'none';
                            analysisContent.style.whiteSpace = 'pre-line';
                            analysisContent.textContent = recommendation.detailed_analysis;

                            analysisContainer.appendChild(analysisHeader);
                            analysisContainer.appendChild(analysisContent);

                            body.appendChild(analysisContainer);

                            // Add event listener to toggle button
                            toggleButton.addEventListener('click', function() {
                                if (analysisContent.style.display === 'none') {
                                    analysisContent.style.display = 'block';
                                    toggleButton.textContent = 'Hide';
                                } else {
                                    analysisContent.style.display = 'none';
                                    toggleButton.textContent = 'Show';
                                }
                            });
                        } catch (error) {
                            console.error('Error adding detailed analysis:', error);
                        }
                    }

                    card.appendChild(body);
                    console.log('Successfully created recommendation card');
                    return card;
                } catch (error) {
                    console.error('Error creating recommendation card:', error);

                    // Create a simple error card instead
                    const errorBody = document.createElement('div');
                    errorBody.className = 'recommendation-body';

                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger';
                    errorAlert.textContent = 'Error creating recommendation card: ' + error.message;

                    errorBody.appendChild(errorAlert);
                    card.appendChild(errorBody);

                    return card;
                }
            }

            function addRecommendationRow(container, label, value) {
                const row = document.createElement('div');
                row.className = 'recommendation-row';

                const labelEl = document.createElement('div');
                labelEl.className = 'recommendation-label';
                labelEl.textContent = label;

                const valueEl = document.createElement('div');
                valueEl.className = 'recommendation-value';
                valueEl.textContent = value;

                row.appendChild(labelEl);
                row.appendChild(valueEl);
                container.appendChild(row);
            }

            // Helper functions
            function getBiasClass(bias) {
                if (bias === 'Bullish') {
                    return 'bg-success';
                } else if (bias === 'Bearish') {
                    return 'bg-danger';
                } else {
                    return 'bg-secondary';
                }
            }

            function showLoading() {
                loadingOverlay.classList.remove('d-none');
            }

            function hideLoading() {
                loadingOverlay.classList.add('d-none');
            }

            function showError(message) {
                console.log('Showing error:', message);
                if (errorMessage) {
                    errorMessage.textContent = message;
                    errorMessage.classList.remove('d-none');
                } else {
                    console.error('Error message element not found');
                    alert('Error: ' + message);
                }
            }

            function hideError() {
                if (errorMessage) {
                    errorMessage.classList.add('d-none');
                }
            }

            function fetchInstitutionalAnalysis() {
                if (!currentOptionsData || currentOptionsData.length === 0 || !currentUnderlyingValue) {
                    // Show error message when no real data is available
                    console.log('No real data available for institutional analysis');
                    displayInstitutionalAnalysis({
                        success: false,
                        message: "Real-time data from Fyers API is required for institutional analysis. Please ensure you have valid API access and try again."
                    });
                    return;
                }

                // Show loading
                showLoading();

                // Call the institutional analysis API
                fetch('/api/institutional-analysis/all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        options_data: currentOptionsData,
                        current_price: currentUnderlyingValue
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();

                    if (data.success) {
                        // Display the institutional analysis results
                        displayInstitutionalAnalysis(data);
                    } else {
                        showError(data.message || 'Failed to perform institutional analysis');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('Error performing institutional analysis: ' + error.message);
                });
            }

            function displayInstitutionalAnalysis(data) {
                const resultsContainer = document.getElementById('institutionalAnalysisResults');

                // Clear previous results
                resultsContainer.innerHTML = '';

                // Create container for the analysis
                const analysisContainer = document.createElement('div');

                // Add combined analysis at the top
                if (data.combined_analysis) {
                    const combinedAnalysis = document.createElement('div');
                    combinedAnalysis.className = 'mb-4 p-3 bg-light rounded';

                    const combinedTitle = document.createElement('h5');
                    combinedTitle.className = 'mb-3';
                    combinedTitle.textContent = 'Institutional Analysis Summary';

                    const combinedContent = document.createElement('div');
                    combinedContent.style.whiteSpace = 'pre-line';
                    combinedContent.textContent = data.combined_analysis;

                    combinedAnalysis.appendChild(combinedTitle);
                    combinedAnalysis.appendChild(combinedContent);
                    analysisContainer.appendChild(combinedAnalysis);
                }

                // Create accordion for detailed analysis sections
                const accordion = document.createElement('div');
                accordion.className = 'accordion';
                accordion.id = 'institutionalAccordion';

                // Add sections for each analysis type
                let accordionIndex = 1;

                // Volume-OI Spikes
                if (data.volume_oi_spikes && data.volume_oi_spikes.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Volume-OI Combined Spike Analysis',
                        createVolumeOISpikesContent(data.volume_oi_spikes)
                    );
                }

                // Dealer Gamma
                if (data.dealer_gamma && data.dealer_gamma.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Dealer Gamma Exposure Analysis',
                        createDealerGammaContent(data.dealer_gamma)
                    );
                }

                // IV Skew
                if (data.iv_skew && data.iv_skew.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'IV Skew Analysis',
                        createIVSkewContent(data.iv_skew)
                    );
                }

                // Delta-Weighted OI
                if (data.delta_weighted_oi && data.delta_weighted_oi.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Delta-Weighted OI Analysis',
                        createDeltaWeightedOIContent(data.delta_weighted_oi)
                    );
                }

                // Liquidity Depth
                if (data.liquidity_depth && data.liquidity_depth.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Liquidity Depth Analysis',
                        createLiquidityDepthContent(data.liquidity_depth)
                    );
                }

                // Smart Money
                if (data.smart_money && data.smart_money.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Smart Money Analysis',
                        createSmartMoneyContent(data.smart_money)
                    );
                }

                // Max Pain Shift
                if (data.max_pain_shift && data.max_pain_shift.success) {
                    addAccordionItem(
                        accordion,
                        accordionIndex++,
                        'Max Pain Shift Analysis',
                        createMaxPainShiftContent(data.max_pain_shift)
                    );
                }

                analysisContainer.appendChild(accordion);
                resultsContainer.appendChild(analysisContainer);
            }

            function addAccordionItem(accordion, index, title, contentElement) {
                const itemId = `accordion-item-${index}`;
                const headerId = `accordion-header-${index}`;
                const collapseId = `accordion-collapse-${index}`;

                const accordionItem = document.createElement('div');
                accordionItem.className = 'accordion-item';

                const header = document.createElement('h2');
                header.className = 'accordion-header';
                header.id = headerId;

                const button = document.createElement('button');
                button.className = 'accordion-button collapsed';
                button.type = 'button';
                button.setAttribute('data-bs-toggle', 'collapse');
                button.setAttribute('data-bs-target', `#${collapseId}`);
                button.setAttribute('aria-expanded', 'false');
                button.setAttribute('aria-controls', collapseId);
                button.textContent = title;

                header.appendChild(button);

                const collapseDiv = document.createElement('div');
                collapseDiv.id = collapseId;
                collapseDiv.className = 'accordion-collapse collapse';
                collapseDiv.setAttribute('aria-labelledby', headerId);
                collapseDiv.setAttribute('data-bs-parent', '#institutionalAccordion');

                const body = document.createElement('div');
                body.className = 'accordion-body';
                body.appendChild(contentElement);

                collapseDiv.appendChild(body);

                accordionItem.appendChild(header);
                accordionItem.appendChild(collapseDiv);

                accordion.appendChild(accordionItem);
            }

            function createVolumeOISpikesContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.summary;
                container.appendChild(analysis);

                // Add fresh positions table if available
                if (data.fresh_positions && data.fresh_positions.length > 0) {
                    const freshTitle = document.createElement('h6');
                    freshTitle.className = 'mt-3 mb-2';
                    freshTitle.textContent = 'Fresh Positions (High Volume + OI Increase)';
                    container.appendChild(freshTitle);

                    const freshTable = createDataTable(['Strike', 'Option Type', 'Volume', 'OI Change', 'Signal', 'Strength']);

                    data.fresh_positions.forEach(position => {
                        const row = freshTable.insertRow();
                        row.insertCell().textContent = position.strike;
                        row.insertCell().textContent = position.option_type;
                        row.insertCell().textContent = position.volume.toLocaleString();
                        row.insertCell().textContent = position.oi_change.toLocaleString();
                        row.insertCell().textContent = position.signal;
                        row.insertCell().textContent = position.strength;
                    });

                    container.appendChild(freshTable);
                }

                // Add closing positions table if available
                if (data.closing_positions && data.closing_positions.length > 0) {
                    const closingTitle = document.createElement('h6');
                    closingTitle.className = 'mt-4 mb-2';
                    closingTitle.textContent = 'Closing Positions (High Volume + OI Decrease)';
                    container.appendChild(closingTitle);

                    const closingTable = createDataTable(['Strike', 'Option Type', 'Volume', 'OI Change', 'Signal', 'Strength']);

                    data.closing_positions.forEach(position => {
                        const row = closingTable.insertRow();
                        row.insertCell().textContent = position.strike;
                        row.insertCell().textContent = position.option_type;
                        row.insertCell().textContent = position.volume.toLocaleString();
                        row.insertCell().textContent = position.oi_change.toLocaleString();
                        row.insertCell().textContent = position.signal;
                        row.insertCell().textContent = position.strength;
                    });

                    container.appendChild(closingTable);
                }

                return container;
            }

            function createDealerGammaContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add dealer positioning info
                const positioningDiv = document.createElement('div');
                positioningDiv.className = 'alert alert-info';
                positioningDiv.textContent = `Dealer Positioning: ${data.dealer_positioning}`;
                container.appendChild(positioningDiv);

                // Add gamma values
                const gammaValues = document.createElement('div');
                gammaValues.className = 'row mb-3';

                const totalGammaCol = document.createElement('div');
                totalGammaCol.className = 'col-md-4';
                totalGammaCol.innerHTML = `<strong>Total Gamma:</strong> ${data.total_gamma.toLocaleString()}`;

                const dealerGammaCol = document.createElement('div');
                dealerGammaCol.className = 'col-md-4';
                dealerGammaCol.innerHTML = `<strong>Dealer Gamma:</strong> ${data.total_dealer_gamma.toLocaleString()}`;

                const customerGammaCol = document.createElement('div');
                customerGammaCol.className = 'col-md-4';
                customerGammaCol.innerHTML = `<strong>Customer Gamma:</strong> ${data.total_customer_gamma.toLocaleString()}`;

                gammaValues.appendChild(totalGammaCol);
                gammaValues.appendChild(dealerGammaCol);
                gammaValues.appendChild(customerGammaCol);

                container.appendChild(gammaValues);

                // Add gamma flip points if available
                if (data.gamma_flip_points && data.gamma_flip_points.length > 0) {
                    const flipPointsTitle = document.createElement('h6');
                    flipPointsTitle.className = 'mt-3 mb-2';
                    flipPointsTitle.textContent = 'Gamma Flip Points';
                    container.appendChild(flipPointsTitle);

                    const flipPointsList = document.createElement('ul');
                    data.gamma_flip_points.forEach(point => {
                        const item = document.createElement('li');
                        item.textContent = point;
                        flipPointsList.appendChild(item);
                    });

                    container.appendChild(flipPointsList);
                }

                return container;
            }

            function createIVSkewContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add IV skew metrics
                const skewMetrics = document.createElement('div');
                skewMetrics.className = 'row mb-3';

                const callSkewCol = document.createElement('div');
                callSkewCol.className = 'col-md-3';
                callSkewCol.innerHTML = `<strong>Call Skew:</strong> ${data.iv_skew.call_skew.toFixed(2)}%`;

                const putSkewCol = document.createElement('div');
                putSkewCol.className = 'col-md-3';
                putSkewCol.innerHTML = `<strong>Put Skew:</strong> ${data.iv_skew.put_skew.toFixed(2)}%`;

                const wingsSkewCol = document.createElement('div');
                wingsSkewCol.className = 'col-md-3';
                wingsSkewCol.innerHTML = `<strong>Wings Skew:</strong> ${data.iv_skew.wings_skew.toFixed(2)}%`;

                const putCallSkewCol = document.createElement('div');
                putCallSkewCol.className = 'col-md-3';
                putCallSkewCol.innerHTML = `<strong>Put-Call Skew:</strong> ${data.iv_skew.put_call_skew.toFixed(2)}%`;

                skewMetrics.appendChild(callSkewCol);
                skewMetrics.appendChild(putSkewCol);
                skewMetrics.appendChild(wingsSkewCol);
                skewMetrics.appendChild(putCallSkewCol);

                container.appendChild(skewMetrics);

                // Add IV data table
                const ivDataTitle = document.createElement('h6');
                ivDataTitle.className = 'mt-3 mb-2';
                ivDataTitle.textContent = 'IV by Moneyness';
                container.appendChild(ivDataTitle);

                const ivTable = document.createElement('table');
                ivTable.className = 'table table-sm table-bordered';

                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                const headers = ['', 'Deep OTM', 'OTM', 'ATM', 'ITM', 'Deep ITM'];
                headers.forEach(header => {
                    const th = document.createElement('th');
                    th.textContent = header;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                ivTable.appendChild(thead);

                const tbody = document.createElement('tbody');

                // Call options row
                const callRow = document.createElement('tr');
                const callHeader = document.createElement('th');
                callHeader.textContent = 'Calls';
                callRow.appendChild(callHeader);

                callRow.appendChild(createCell(data.iv_data.deep_otm_calls));
                callRow.appendChild(createCell(data.iv_data.otm_calls));
                callRow.appendChild(createCell(data.iv_data.atm_calls));
                callRow.appendChild(createCell(data.iv_data.itm_calls));
                callRow.appendChild(createCell(data.iv_data.deep_itm_calls));

                tbody.appendChild(callRow);

                // Put options row
                const putRow = document.createElement('tr');
                const putHeader = document.createElement('th');
                putHeader.textContent = 'Puts';
                putRow.appendChild(putHeader);

                putRow.appendChild(createCell(data.iv_data.deep_otm_puts));
                putRow.appendChild(createCell(data.iv_data.otm_puts));
                putRow.appendChild(createCell(data.iv_data.atm_puts));
                putRow.appendChild(createCell(data.iv_data.itm_puts));
                putRow.appendChild(createCell(data.iv_data.deep_itm_puts));

                tbody.appendChild(putRow);
                ivTable.appendChild(tbody);

                container.appendChild(ivTable);

                return container;

                function createCell(value) {
                    const cell = document.createElement('td');
                    cell.textContent = value ? value.toFixed(2) + '%' : 'N/A';
                    return cell;
                }
            }

            function createDeltaWeightedOIContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add total delta-weighted OI
                const totalDiv = document.createElement('div');
                totalDiv.className = 'alert alert-info';
                totalDiv.innerHTML = `<strong>Net Delta-Weighted OI:</strong> ${data.total_delta_weighted_oi.toLocaleString()}`;
                container.appendChild(totalDiv);

                // Add concentration points if available
                if (data.concentration_points && data.concentration_points.length > 0) {
                    const concentrationTitle = document.createElement('h6');
                    concentrationTitle.className = 'mt-3 mb-2';
                    concentrationTitle.textContent = 'Delta-Weighted OI Concentration Points';
                    container.appendChild(concentrationTitle);

                    const concentrationTable = createDataTable(['Strike', 'Delta-Weighted OI']);

                    data.concentration_points.forEach(point => {
                        const row = concentrationTable.insertRow();
                        row.insertCell().textContent = point['Strike Price'];
                        row.insertCell().textContent = point['Delta_Weighted_OI'].toLocaleString();
                    });

                    container.appendChild(concentrationTable);
                }

                // Add zero-cross points if available
                if (data.zero_cross_points && data.zero_cross_points.length > 0) {
                    const zeroTitle = document.createElement('h6');
                    zeroTitle.className = 'mt-4 mb-2';
                    zeroTitle.textContent = 'Delta-Weighted OI Zero-Cross Points';
                    container.appendChild(zeroTitle);

                    const zeroList = document.createElement('ul');
                    data.zero_cross_points.forEach(point => {
                        const item = document.createElement('li');
                        item.textContent = point;
                        zeroList.appendChild(item);
                    });

                    container.appendChild(zeroList);
                }

                return container;
            }

            function createLiquidityDepthContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add most liquid strikes if available
                if (data.most_liquid_strikes && data.most_liquid_strikes.length > 0) {
                    const liquidTitle = document.createElement('h6');
                    liquidTitle.className = 'mt-3 mb-2';
                    liquidTitle.textContent = 'Most Liquid Options';
                    container.appendChild(liquidTitle);

                    const liquidTable = createDataTable(['Strike', 'Option Type', 'Volume', 'Spread %', 'Liquidity Score']);

                    data.most_liquid_strikes.forEach(strike => {
                        const row = liquidTable.insertRow();
                        row.insertCell().textContent = strike['Strike Price'];
                        row.insertCell().textContent = strike['Option Type'];
                        row.insertCell().textContent = strike['Volume'].toLocaleString();
                        row.insertCell().textContent = strike['Spread_Pct'].toFixed(2) + '%';
                        row.insertCell().textContent = strike['Liquidity_Score'].toFixed(2);
                    });

                    container.appendChild(liquidTable);
                }

                return container;
            }

            function createSmartMoneyContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add smart money options if available
                if (data.smart_money_options && data.smart_money_options.length > 0) {
                    const smartTitle = document.createElement('h6');
                    smartTitle.className = 'mt-3 mb-2';
                    smartTitle.textContent = 'Smart Money Activity';
                    container.appendChild(smartTitle);

                    const smartTable = createDataTable(['Strike', 'Option Type', 'Volume', 'OI', 'OI Change', 'Smart Money Score']);

                    data.smart_money_options.forEach(option => {
                        const row = smartTable.insertRow();
                        row.insertCell().textContent = option['Strike Price'];
                        row.insertCell().textContent = option['Option Type'];
                        row.insertCell().textContent = option['Volume'].toLocaleString();
                        row.insertCell().textContent = option['OI'].toLocaleString();
                        row.insertCell().textContent = option['Change in OI'].toLocaleString();
                        row.insertCell().textContent = option['Smart_Money_Score'].toFixed(2);
                    });

                    container.appendChild(smartTable);
                }

                // Add block trades if available
                if (data.block_trades && data.block_trades.length > 0) {
                    const blockTitle = document.createElement('h6');
                    blockTitle.className = 'mt-4 mb-2';
                    blockTitle.textContent = 'Block Trades';
                    container.appendChild(blockTitle);

                    const blockTable = createDataTable(['Strike', 'Option Type', 'Volume', 'OI']);

                    data.block_trades.forEach(trade => {
                        const row = blockTable.insertRow();
                        row.insertCell().textContent = trade['Strike Price'];
                        row.insertCell().textContent = trade['Option Type'];
                        row.insertCell().textContent = trade['Volume'].toLocaleString();
                        row.insertCell().textContent = trade['OI'].toLocaleString();
                    });

                    container.appendChild(blockTable);
                }

                return container;
            }

            function createMaxPainShiftContent(data) {
                const container = document.createElement('div');

                // Add analysis text
                const analysis = document.createElement('p');
                analysis.className = 'mb-4';
                analysis.textContent = data.analysis;
                container.appendChild(analysis);

                // Add current max pain
                const maxPainDiv = document.createElement('div');
                maxPainDiv.className = 'alert alert-info';
                maxPainDiv.innerHTML = `<strong>Current Max Pain:</strong> ${data.current_max_pain}`;
                container.appendChild(maxPainDiv);

                // Add max pain shift metrics
                const shiftMetrics = document.createElement('div');
                shiftMetrics.className = 'row mb-3';

                const dailyShiftCol = document.createElement('div');
                dailyShiftCol.className = 'col-md-3';
                dailyShiftCol.innerHTML = `<strong>Daily Shift:</strong> ${data.max_pain_shift.daily_shift}`;

                const weeklyShiftCol = document.createElement('div');
                weeklyShiftCol.className = 'col-md-3';
                weeklyShiftCol.innerHTML = `<strong>Weekly Shift:</strong> ${data.max_pain_shift.weekly_shift}`;

                const trendCol = document.createElement('div');
                trendCol.className = 'col-md-3';
                trendCol.innerHTML = `<strong>Trend:</strong> ${data.max_pain_shift.trend}`;

                const accelerationCol = document.createElement('div');
                accelerationCol.className = 'col-md-3';
                accelerationCol.innerHTML = `<strong>Acceleration:</strong> ${data.max_pain_shift.acceleration}`;

                shiftMetrics.appendChild(dailyShiftCol);
                shiftMetrics.appendChild(weeklyShiftCol);
                shiftMetrics.appendChild(trendCol);
                shiftMetrics.appendChild(accelerationCol);

                container.appendChild(shiftMetrics);

                // Add historical data if available
                if (data.historical_max_pain && data.historical_max_pain.dates.length > 0) {
                    const historyTitle = document.createElement('h6');
                    historyTitle.className = 'mt-3 mb-2';
                    historyTitle.textContent = 'Historical Max Pain';
                    container.appendChild(historyTitle);

                    const historyTable = createDataTable(['Date', 'Max Pain', 'Price']);

                    for (let i = 0; i < data.historical_max_pain.dates.length; i++) {
                        const row = historyTable.insertRow();
                        row.insertCell().textContent = data.historical_max_pain.dates[i];
                        row.insertCell().textContent = data.historical_max_pain.values[i];
                        row.insertCell().textContent = data.historical_max_pain.price[i];
                    }

                    container.appendChild(historyTable);
                }

                return container;
            }

            function createDataTable(headers) {
                const table = document.createElement('table');
                table.className = 'table table-sm table-striped';

                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                headers.forEach(header => {
                    const th = document.createElement('th');
                    th.textContent = header;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);
                table.appendChild(document.createElement('tbody'));

                return table;
            }
        });
    </script>

    <!-- Chart.js Annotation plugin -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
</body>
</html>
