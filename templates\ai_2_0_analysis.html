<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 2.0 Analysis</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.css" rel="stylesheet" type="text/css">
</head>
<body>
    <div class="container">
        <h1>AI 2.0 Fundamental Analysis</h1>

        <div class="form-group">
            <label for="stockSelect">Select Stock:</label>
            <select id="stockSelect" class="form-control" style="width: 100%;"></select>
        </div>

        <button id="analyzeButton" class="btn btn-primary mt-3">Perform Analysis</button>

        <div id="loadingSpinner" class="spinner-border text-primary mt-3" role="status" style="display:none;">
            <span class="sr-only">Loading...</span>
        </div>

        <div id="analysisResult" class="mt-4" style="display:none;">
            <h2>Analysis Result for <span id="analyzedStockName"></span></h2>
            <div id="jsonEditor" style="width: 100%; height: 600px;"></div>
        </div>

        <div id="errorMessage" class="alert alert-danger mt-3" style="display:none;"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.js"></script>
    <script>
        let editor; // Declare editor globally

        $(document).ready(function() {
            // Initialize Select2 for stock dropdown
            $('#stockSelect').select2({
                placeholder: 'Search for a stock',
                ajax: {
                    url: '/api/nse-stocks', // Endpoint to fetch FNO stocks
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            search: params.term // search term
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data.stocks.map(function(stock) {
                                return {
                                    id: stock.symbol, // Use symbol as ID
                                    text: stock.symbol // Display symbol
                                };
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 1 // Minimum characters to start searching
            });

            // Initialize JSON Editor
            const container = document.getElementById("jsonEditor");
            const options = {
                mode: 'view',
                modes: ['view', 'code'], // allowed modes
                onError: function (err) {
                    alert(err.toString());
                }
            };
            editor = new JSONEditor(container, options);

            $('#analyzeButton').on('click', function() {
                const selectedStock = $('#stockSelect').val();
                if (selectedStock) {
                    performAnalysis(selectedStock);
                } else {
                    displayMessage('Please select a stock to analyze.', 'danger');
                }
            });
        });

        function performAnalysis(stockName) {
            $('#loadingSpinner').show();
            $('#analysisResult').hide();
            $('#errorMessage').hide();
            $('#analyzedStockName').text(stockName);

            fetch('/api/ai-2-0-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ stock_name: stockName }),
            })
            .then(response => response.json())
            .then(data => {
                $('#loadingSpinner').hide();
                if (data.success) {
                    editor.set(data.analysis_result);
                    $('#analysisResult').show();
                } else {
                    displayMessage(data.message || 'An unknown error occurred during analysis.', 'danger');
                }
            })
            .catch(error => {
                $('#loadingSpinner').hide();
                console.error('Error:', error);
                displayMessage('An error occurred while fetching analysis: ' + error.message, 'danger');
            });
        }

        function displayMessage(message, type) {
            const messageElement = $('#errorMessage');
            messageElement.text(message);
            messageElement.removeClass('alert-success alert-info alert-warning alert-danger').addClass('alert-' + type);
            messageElement.show();
        }
    </script>
</body>
</html> 