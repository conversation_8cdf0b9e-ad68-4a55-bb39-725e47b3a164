# AI-Powered Options Analysis Application with Fyers API Integration

A comprehensive options analysis tool that integrates with the Fyers API and Google's Gemini 2.0 AI model to provide real-time option chain data and advanced AI-powered analysis capabilities.

## 🚀 New Features

### **AI-Powered Analysis with Gemini 2.0**
- **Advanced AI Analysis**: Leverage Google's Gemini 2.0 Flash model for sophisticated options analysis
- **Gamma Acceleration Strategy**: AI-driven gamma acceleration trading strategy implementation
- **Intelligent Trade Recommendations**: AI-generated trade setups with entry, exit, and risk management
- **Natural Language Insights**: Human-readable analysis and explanations from AI
- **Comparison Mode**: Side-by-side comparison of AI analysis vs traditional mathematical analysis

## Features

- Advanced options analysis (Max Pain, Gamma Exposure, PCR, etc.)
- Institutional-level analysis (Volume-OI spikes, Dealer Gamma, IV Skew, etc.)
- Batch analysis for multiple stocks
- Trade recommendations with scoring and detailed analysis
- Watchlist management
- Fyers API integration

## Deployment on Render

### Prerequisites

1. A Render account
2. Fyers API credentials

### Environment Variables

Set the following environment variables in your Render dashboard:

- `FLASK_ENV`: Set to `production`
- `FLASK_SECRET_KEY`: A random secret key for Flask sessions
- `FYERS_CLIENT_ID`: Your Fyers API client ID
- `FYERS_SECRET_KEY`: Your Fyers API secret key
- `FYERS_REDIRECT_URL`: Your Fyers API redirect URL
- `FYERS_ACCESS_TOKEN`: Your Fyers API access token (needs to be updated daily)

### Deployment Steps

#### Option 1: Using render.yaml (Recommended)

1. Fork or clone this repository
2. Push to your own GitHub repository
3. Connect your GitHub repository to Render
4. Use the "Blueprint" deployment option in Render
5. Render will automatically configure the service based on the render.yaml file
6. Add your Fyers API credentials in the environment variables section
7. Deploy the service

#### Option 2: Manual Configuration

1. Connect your GitHub repository to Render
2. Create a new Web Service
3. Select the repository
4. Configure the service:
   - Name: options-analysis (or your preferred name)
   - Environment: Python 3.10
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `gunicorn app:app`
5. Add the environment variables
6. Deploy the service

### Updating the Access Token

The Fyers API access token expires daily. To update it:

1. Go to the Render dashboard
2. Navigate to your web service
3. Go to the Environment tab
4. Update the `FYERS_ACCESS_TOKEN` value
5. Click Save Changes

## Local Development

### Setup

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Mac/Linux: `source venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Create a `.env` file with the required environment variables (see `.env.example`)
6. Generate a secure Flask secret key:
   ```bash
   python -c "import secrets; print(secrets.token_hex(32))"
   ```
   Copy the output and use it as your `FLASK_SECRET_KEY` in the `.env` file
7. Run the application: `python app.py`

### Token Management

In development mode, you can manage your Fyers API token through the Token Manager page in the application.

## Security

### Important Security Notes

⚠️ **Never commit sensitive data to version control:**
- API keys and tokens
- Environment files (`.env`)
- Token files (`tokens/`)
- Any configuration files with credentials

### Environment Variables

All sensitive configuration should be stored in environment variables:
- Copy `.env.example` to `.env`
- Fill in your actual API credentials
- Never commit the `.env` file to git

### API Key Security

- Keep your Fyers API credentials secure
- Regenerate tokens if compromised
- Use environment variables in production
- Never hardcode credentials in source code
