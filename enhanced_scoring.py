"""
Enhanced scoring system for option chain analysis
Includes trend confirmation, volume spike detection, expiry consideration, and news sensitivity
"""

import random

class EnhancedScoring:
    def __init__(self):
        """Initialize the enhanced scoring system"""
        # Weights for different factors
        self.weights = {
            'money_flow': 0.20,  # 20% weight for money flow
            'oi_analysis': 0.30,  # 30% weight for OI analysis
            'fii_dii': 0.20,     # 20% weight for FII/DII activity
            'trend': 0.30        # 30% weight for technical trend
        }

    def check_trend_confirmation(self, symbol):
        """
        Check if the trend confirms the trade using EMA 20/50 crossover on 15min/1hr charts

        Args:
            symbol (str): Symbol to check trend for

        Returns:
            dict: Trend confirmation data
        """
        # Return error if no real data is available
        trend_data = {
            '15min': {
                'ema_20': None,
                'ema_50': None,
                'crossover': None
            },
            '1hr': {
                'ema_20': None,
                'ema_50': None,
                'crossover': None
            }
        }
        
        # Default to neutral trend with low confidence
        trend_bias = 'Neutral'
        trend_score = 0  # Zero score when no real data
        
        # Create analysis text
        analysis = "EMA Analysis: No real technical data available from Fyers API. Cannot determine trend confirmation."
        
        return {
            'trend_bias': trend_bias,
            'trend_score': trend_score,
            'data': trend_data,
            'analysis': analysis,
            'error': 'No real technical data available from Fyers API'
        }

    def check_volume_spike(self, symbol, current_volume):
        """
        Check if there's a volume spike in the underlying stock

        Args:
            symbol (str): Symbol to check volume for
            current_volume (float): Current volume

        Returns:
            dict: Volume spike data
        """
        # Return error if no real historical volume data is available
        if current_volume is None or current_volume <= 0:
            return {
                'spike_level': 'Unknown',
                'volume_ratio': 0,
                'volume_score': 0,
                'analysis': 'Volume Analysis: No valid volume data available from Fyers API.',
                'error': 'No valid volume data available from Fyers API'
            }
            
        # We have current volume but no historical data to compare
        # Return with zero score but include the current volume for reference
        volume_data = {
            'current_volume': current_volume,
            'avg_volume': None,
            'historical_data': None
        }
        
        # Create analysis text
        analysis = f"Volume Analysis: Current volume is {current_volume:,}, but no historical data available from Fyers API to determine if this represents a spike."
        
        return {
            'spike_level': 'Unknown',
            'volume_ratio': 0,
            'volume_score': 0,
            'volume_data': volume_data,
            'analysis': analysis,
            'error': 'No historical volume data available from Fyers API for comparison'
        }

    def check_expiry_consideration(self, days_to_expiry):
        """
        Consider days to expiry in the scoring

        Args:
            days_to_expiry (int or str): Days to expiry

        Returns:
            dict: Expiry consideration data
        """
        # Ensure days_to_expiry is an integer
        try:
            if days_to_expiry is None:
                days_to_expiry = 7  # Default to a week if not provided
            else:
                days_to_expiry = int(days_to_expiry)  # Convert to integer if it's a string
        except (ValueError, TypeError):
            days_to_expiry = 7  # Default to a week if conversion fails

        # Determine expiry risk level
        if days_to_expiry <= 1:
            risk_level = 'Extreme'
            expiry_score = 2
            recommendation = "Extremely high theta decay risk. Only consider deep ITM options or very tight stop loss."
        elif days_to_expiry <= 3:
            risk_level = 'High'
            expiry_score = 4
            recommendation = "High theta decay risk. Prefer ITM options or tighter stop loss."
        elif days_to_expiry <= 7:
            risk_level = 'Moderate'
            expiry_score = 6
            recommendation = "Moderate theta decay risk. Consider ATM or slight ITM options."
        elif days_to_expiry <= 14:
            risk_level = 'Low'
            expiry_score = 8
            recommendation = "Low theta decay risk. ATM or slight OTM options can be considered."
        else:
            risk_level = 'Very Low'
            expiry_score = 10
            recommendation = "Very low theta decay risk. OTM options can be considered for higher leverage."

        # Create analysis text
        analysis = f"Expiry Analysis: {days_to_expiry} days to expiry, indicating {risk_level.lower()} theta decay risk. {recommendation}"

        return {
            'risk_level': risk_level,
            'expiry_score': expiry_score,
            'recommendation': recommendation,
            'analysis': analysis,
            'days_to_expiry': days_to_expiry  # Store the converted integer value
        }

    def check_news_sensitivity(self, _symbol):
        """
        Check if there are any upcoming news events that might affect the trade

        Args:
            _symbol (str): Symbol to check news for

        Returns:
            dict: News sensitivity data
        """
        # Return error since no real news data is available from Fyers API
        return {
            'success': False,
            'message': 'News data is not available through Fyers API. Please check external news sources for upcoming events.',
            'risk_level': 'Unknown',
            'news_score': 0,
            'upcoming_events': []
        }

    def calculate_weighted_score(self, option_data, money_flow_data, trend_data, volume_data, expiry_data, news_data, option_type, _market_bias):
        """
        Calculate a weighted score for an option based on all factors

        Args:
            option_data (dict): Option data
            money_flow_data (dict): Money flow data
            trend_data (dict): Trend confirmation data
            volume_data (dict): Volume spike data
            expiry_data (dict): Expiry consideration data
            news_data (dict): News sensitivity data
            option_type (str): 'call' or 'put'
            _market_bias (str): Overall market bias

        Returns:
            dict: Weighted score and detailed breakdown
        """
        # Initialize score components
        score_components = {}
        missing_data_components = []

        # 1. Money Flow Score (20%)
        money_flow_score = 0
        if money_flow_data and money_flow_data.get('success', False):
            # For calls, positive money flow is good
            if option_type.lower() == 'call':
                if money_flow_data.get('flow_bias') == 'Bullish':
                    money_flow_score = 10
                elif money_flow_data.get('flow_bias') == 'Neutral':
                    money_flow_score = 5
                elif money_flow_data.get('flow_bias') == 'Bearish':
                    money_flow_score = 2
                else:  # Unknown or missing
                    money_flow_score = 0
                    missing_data_components.append('money_flow')
            # For puts, negative money flow is good
            else:
                if money_flow_data.get('flow_bias') == 'Bearish':
                    money_flow_score = 10
                elif money_flow_data.get('flow_bias') == 'Neutral':
                    money_flow_score = 5
                elif money_flow_data.get('flow_bias') == 'Bullish':
                    money_flow_score = 2
                else:  # Unknown or missing
                    money_flow_score = 0
                    missing_data_components.append('money_flow')

            # Adjust based on strength if available
            if money_flow_data.get('flow_strength') == 'Strong':
                money_flow_score = min(10, money_flow_score * 1.2)
            elif money_flow_data.get('flow_strength') == 'Moderate':
                money_flow_score = money_flow_score
            elif money_flow_data.get('flow_strength') == 'Weak':
                money_flow_score = money_flow_score * 0.8
        else:
            missing_data_components.append('money_flow')

        score_components['money_flow'] = money_flow_score

        # 2. OI Analysis Score (30%)
        oi_score = 0
        if 'OI' in option_data and 'Change in OI' in option_data:
            oi = option_data.get('OI', 0)
            oi_change = option_data.get('Change in OI', 0)

            # Check if we have real OI data
            if oi > 0 or oi_change != 0:
                # For calls, positive OI change is good
                if option_type.lower() == 'call':
                    if oi_change > 0:
                        oi_score = 8
                    elif oi_change < 0:
                        oi_score = 3
                    else:
                        oi_score = 5
                # For puts, negative OI change is good
                else:
                    if oi_change < 0:
                        oi_score = 8
                    elif oi_change > 0:
                        oi_score = 3
                    else:
                        oi_score = 5

                # Adjust based on OI magnitude
                oi_magnitude = abs(oi_change) / oi if oi > 0 else 0
                if oi_magnitude > 0.1:  # More than 10% change
                    oi_score = min(10, oi_score * 1.25)
            else:
                missing_data_components.append('oi_analysis')
        else:
            missing_data_components.append('oi_analysis')

        score_components['oi_analysis'] = oi_score

        # 3. FII/DII Activity Score (20%)
        fii_dii_score = 0
        if money_flow_data and money_flow_data.get('success', False) and 'institutional_activity' in money_flow_data:
            inst_activity = money_flow_data['institutional_activity']
            if inst_activity and 'fii' in inst_activity and 'dii' in inst_activity:
                fii_net = inst_activity.get('fii', {}).get('net')
                dii_net = inst_activity.get('dii', {}).get('net')
                
                # Check if we have real FII/DII data
                if fii_net is not None and dii_net is not None:
                    # For calls, positive FII/DII activity is good
                    if option_type.lower() == 'call':
                        if fii_net > 0 and dii_net > 0:
                            fii_dii_score = 10  # Both positive
                        elif fii_net > 0 or dii_net > 0:
                            fii_dii_score = 7   # One positive
                        else:
                            fii_dii_score = 3   # Both negative
                    # For puts, negative FII/DII activity is good
                    else:
                        if fii_net < 0 and dii_net < 0:
                            fii_dii_score = 10  # Both negative
                        elif fii_net < 0 or dii_net < 0:
                            fii_dii_score = 7   # One negative
                        else:
                            fii_dii_score = 3   # Both positive
                else:
                    missing_data_components.append('fii_dii')
            else:
                missing_data_components.append('fii_dii')
        else:
            missing_data_components.append('fii_dii')

        score_components['fii_dii'] = fii_dii_score

        # 4. Technical Trend Score (30%)
        trend_score = trend_data.get('trend_score', 0)
        if trend_score == 0:
            missing_data_components.append('trend')
        score_components['trend'] = trend_score

        # Calculate weighted score
        weighted_score = 0
        total_weight = 0
        
        # Only include components that have data
        for component, score in score_components.items():
            if component not in missing_data_components:
                weighted_score += score * self.weights[component]
                total_weight += self.weights[component]
        
        # If we have no data at all, return a score of 0
        if total_weight == 0:
            return {
                'score': 0,
                'components': score_components,
                'weights': self.weights,
                'adjustments': {},
                'error': 'No real data available from Fyers API to calculate score',
                'missing_components': missing_data_components
            }
        
        # Normalize based on available weights
        weighted_score = weighted_score / total_weight if total_weight > 0 else 0
        
        # Normalize to 0-100 scale
        normalized_score = min(100, max(0, weighted_score * 10))

        # Additional adjustments
        adjustments = {}
        
        # Expiry risk adjustment
        if expiry_data and expiry_data.get('risk_level') in ['High', 'Extreme']:
            normalized_score *= 0.9  # Reduce score by 10% for high expiry risk
            adjustments['expiry_risk'] = expiry_data.get('risk_level')

        # Volume spike adjustment
        if volume_data and volume_data.get('spike_level') == 'High':
            normalized_score *= 1.1  # Increase score by 10% for high volume spike
            normalized_score = min(100, normalized_score)  # Cap at 100
            adjustments['volume_spike'] = volume_data.get('spike_level')

        # News risk adjustment
        if news_data and news_data.get('risk_level') in ['Medium', 'High']:
            normalized_score *= 0.9  # Reduce score by 10% for high news risk
            adjustments['news_risk'] = news_data.get('risk_level')

        # Round to integer
        final_score = round(normalized_score)
        
        # Confidence level based on missing data
        confidence_level = 'High'
        if len(missing_data_components) >= 3:
            confidence_level = 'Low'
        elif len(missing_data_components) >= 1:
            confidence_level = 'Medium'

        return {
            'score': final_score,
            'components': score_components,
            'weights': self.weights,
            'adjustments': adjustments,
            'confidence_level': confidence_level,
            'missing_components': missing_data_components
        }

    def enhance_trade_recommendations(self, recommendations, symbol, _options_data=None, _current_price=None, days_to_expiry=None):
        """
        Enhance trade recommendations with additional analysis factors

        Args:
            recommendations (dict): Original trade recommendations
            symbol (str): Symbol being analyzed
            _options_data (list, optional): Options data. Defaults to None.
            _current_price (float, optional): Current price. Defaults to None.
            days_to_expiry (int, optional): Days to expiry. Defaults to None.

        Returns:
            dict: Enhanced trade recommendations
        """
        if not recommendations:
            return {}

        # Get trend confirmation data
        trend_data = self.check_trend_confirmation(symbol)

        # Get volume spike data - use actual volume if available
        current_volume = 0
        if _options_data and len(_options_data) > 0:
            # Try to find volume from the underlying
            for option in _options_data:
                if option.get('Strike Price', 0) == -1:  # This is the underlying
                    current_volume = option.get('Volume', 0)
                    break
        
        volume_data = self.check_volume_spike(symbol, current_volume)

        # Get expiry consideration data
        expiry_data = self.check_expiry_consideration(days_to_expiry)

        # Get news sensitivity data
        news_data = self.check_news_sensitivity(symbol)

        # Get money flow data from recommendations
        money_flow_data = recommendations.get('money_flow_data', {})

        # Add enhanced analysis
        enhanced_analysis = []

        # Add trend analysis
        if trend_data and trend_data.get('analysis'):
            enhanced_analysis.append(trend_data['analysis'])

        # Add volume analysis
        if volume_data and volume_data.get('analysis'):
            enhanced_analysis.append(volume_data['analysis'])

        # Add expiry analysis
        if expiry_data and expiry_data.get('analysis'):
            enhanced_analysis.append(expiry_data['analysis'])

        # Add news analysis
        if news_data and news_data.get('analysis'):
            enhanced_analysis.append(news_data['analysis'])

        # Store analysis data for UI
        recommendations['trend_data'] = trend_data
        recommendations['volume_data'] = volume_data
        recommendations['expiry_data'] = expiry_data
        recommendations['news_data'] = news_data
        recommendations['enhanced_analysis'] = ' '.join(enhanced_analysis)

        # Check if we have any real data to work with
        missing_data_warning = None
        if (trend_data.get('error') and volume_data.get('error') and 
            news_data.get('error') and not money_flow_data.get('success', False)):
            missing_data_warning = "Warning: Limited real data available from Fyers API. Scores may not be accurate."
            recommendations['data_quality'] = 'Poor'
        elif (trend_data.get('error') or volume_data.get('error') or 
              news_data.get('error') or not money_flow_data.get('success', False)):
            missing_data_warning = "Note: Some real data components missing from Fyers API. Scores may be less reliable."
            recommendations['data_quality'] = 'Fair'
        else:
            recommendations['data_quality'] = 'Good'

        if missing_data_warning:
            enhanced_analysis.append(missing_data_warning)
            recommendations['enhanced_analysis'] = ' '.join(enhanced_analysis)

        # Enhance call recommendations
        for trade in recommendations.get('calls', []):
            # Calculate enhanced score
            option_data = trade.get('option_data', {})
            enhanced_score_data = self.calculate_weighted_score(
                option_data,
                money_flow_data,
                trend_data,
                volume_data,
                expiry_data,
                news_data,
                'call',
                recommendations.get('market_bias', 'Neutral')
            )

            # Add enhanced score to trade
            trade['enhanced_score'] = enhanced_score_data['score']
            trade['score_breakdown'] = enhanced_score_data
            trade['score_confidence'] = enhanced_score_data.get('confidence_level', 'Low')
            
            # Add missing components info
            if 'missing_components' in enhanced_score_data and enhanced_score_data['missing_components']:
                trade['missing_data'] = enhanced_score_data['missing_components']

            # Add additional analysis to description
            if 'description' not in trade:
                trade['description'] = ''

            # Add trend confirmation if available
            if trend_data.get('trend_bias') != 'Neutral' and not trend_data.get('error'):
                trade['description'] += f" {trend_data['trend_bias']} trend on {symbol}."

            # Add volume spike if available
            if volume_data.get('spike_level') == 'High' and not volume_data.get('error'):
                trade['description'] += f" High volume activity detected."

            # Add expiry warning if needed
            if expiry_data['risk_level'] in ['High', 'Extreme']:
                # Get days to expiry from expiry_data to ensure it's the converted integer value
                expiry_days = expiry_data.get('days_to_expiry', days_to_expiry)
                trade['description'] += f" Warning: {expiry_data['risk_level']} theta decay risk with {expiry_days} days to expiry."

            # Add data quality warning if needed
            if missing_data_warning and trade['score_confidence'] != 'High':
                trade['description'] += f" (Score confidence: {trade['score_confidence']})"

        # Enhance put recommendations
        for trade in recommendations.get('puts', []):
            # Calculate enhanced score
            option_data = trade.get('option_data', {})
            enhanced_score_data = self.calculate_weighted_score(
                option_data,
                money_flow_data,
                trend_data,
                volume_data,
                expiry_data,
                news_data,
                'put',
                recommendations.get('market_bias', 'Neutral')
            )

            # Add enhanced score to trade
            trade['enhanced_score'] = enhanced_score_data['score']
            trade['score_breakdown'] = enhanced_score_data
            trade['score_confidence'] = enhanced_score_data.get('confidence_level', 'Low')
            
            # Add missing components info
            if 'missing_components' in enhanced_score_data and enhanced_score_data['missing_components']:
                trade['missing_data'] = enhanced_score_data['missing_components']

            # Add additional analysis to description
            if 'description' not in trade:
                trade['description'] = ''

            # Add trend confirmation if available
            if trend_data.get('trend_bias') != 'Neutral' and not trend_data.get('error'):
                trade['description'] += f" {trend_data['trend_bias']} trend on {symbol}."

            # Add volume spike if available
            if volume_data.get('spike_level') == 'High' and not volume_data.get('error'):
                trade['description'] += f" High volume activity detected."

            # Add expiry warning if needed
            if expiry_data['risk_level'] in ['High', 'Extreme']:
                # Get days to expiry from expiry_data to ensure it's the converted integer value
                expiry_days = expiry_data.get('days_to_expiry', days_to_expiry)
                trade['description'] += f" Warning: {expiry_data['risk_level']} theta decay risk with {expiry_days} days to expiry."

            # Add data quality warning if needed
            if missing_data_warning and trade['score_confidence'] != 'High':
                trade['description'] += f" (Score confidence: {trade['score_confidence']})"

            # Add expiry warning if needed
            if expiry_data['risk_level'] in ['High', 'Extreme']:
                expiry_days = expiry_data.get('days_to_expiry', days_to_expiry)
                trade['description'] += f" Warning: {expiry_data['risk_level']} theta decay risk with {expiry_days} days to expiry."

            # Add news warning if needed
            if news_data['risk_level'] in ['Medium', 'High'] and news_data['upcoming_events']:
                event = news_data['upcoming_events'][0]
                trade['description'] += f" Caution: {event['event']} on {event['date'].strftime('%d-%b')}."

        # Filter and limit recommendations to best 3 options (1 ATM, 1 ITM, 1 OTM)
        enhanced_recommendations = self._filter_and_limit_recommendations(recommendations)

        return enhanced_recommendations

    def _filter_and_limit_recommendations(self, recommendations):
        """
        Filter and limit recommendations to best 3 options (1 ATM, 1 ITM, 1 OTM)

        Args:
            recommendations (dict): Original trade recommendations

        Returns:
            dict: Filtered and limited recommendations
        """
        # Keep the best trade as is
        best_trade = recommendations.get('best_trade')

        # Get all call and put recommendations
        all_calls = recommendations.get('calls', [])
        all_puts = recommendations.get('puts', [])

        # Sort by enhanced score if available, otherwise by original score
        all_calls.sort(key=lambda x: x.get('enhanced_score', x.get('score', 0)), reverse=True)
        all_puts.sort(key=lambda x: x.get('enhanced_score', x.get('score', 0)), reverse=True)

        # Filter calls by moneyness
        atm_calls = [t for t in all_calls if t.get('moneyness') == 'ATM']
        itm_calls = [t for t in all_calls if t.get('moneyness') == 'ITM']
        otm_calls = [t for t in all_calls if t.get('moneyness') == 'OTM']

        # Filter puts by moneyness
        atm_puts = [t for t in all_puts if t.get('moneyness') == 'ATM']
        itm_puts = [t for t in all_puts if t.get('moneyness') == 'ITM']
        otm_puts = [t for t in all_puts if t.get('moneyness') == 'OTM']

        # Select best options from each category
        best_calls = []
        if atm_calls and atm_calls[0].get('enhanced_score', atm_calls[0].get('score', 0)) >= 60:
            best_calls.append(atm_calls[0])
        if itm_calls and itm_calls[0].get('enhanced_score', itm_calls[0].get('score', 0)) >= 60:
            best_calls.append(itm_calls[0])
        if otm_calls and otm_calls[0].get('enhanced_score', otm_calls[0].get('score', 0)) >= 60:
            best_calls.append(otm_calls[0])

        best_puts = []
        if atm_puts and atm_puts[0].get('enhanced_score', atm_puts[0].get('score', 0)) >= 60:
            best_puts.append(atm_puts[0])
        if itm_puts and itm_puts[0].get('enhanced_score', itm_puts[0].get('score', 0)) >= 60:
            best_puts.append(itm_puts[0])
        if otm_puts and otm_puts[0].get('enhanced_score', otm_puts[0].get('score', 0)) >= 60:
            best_puts.append(otm_puts[0])

        # Limit to top 3 calls and puts
        best_calls = best_calls[:3]
        best_puts = best_puts[:3]

        # Create enhanced recommendations
        enhanced_recommendations = {
            'best_trade': best_trade,
            'calls': best_calls,
            'puts': best_puts,
            'market_bias': recommendations.get('market_bias', 'Neutral'),
            'confidence': recommendations.get('confidence', 50),
            'enhanced_analysis': recommendations.get('enhanced_analysis', ''),
            'trend_data': recommendations.get('trend_data', {}),
            'volume_data': recommendations.get('volume_data', {}),
            'expiry_data': recommendations.get('expiry_data', {}),
            'news_data': recommendations.get('news_data', {})
        }

        return enhanced_recommendations
