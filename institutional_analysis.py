import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math
from scipy.stats import norm

class InstitutionalAnalysis:
    """
    Advanced institutional-level options analysis techniques
    """

    def __init__(self):
        """Initialize the institutional analysis module"""
        # Constants for analysis
        self.volume_threshold = 2.0  # Standard deviations above mean for volume spike
        self.oi_threshold = 1.5      # Standard deviations above mean for OI spike
        self.significant_change = 0.1  # 10% change is considered significant

    def detect_volume_oi_spikes(self, options_data):
        """
        Detect combined volume and OI spikes that indicate institutional activity

        Args:
            options_data (list): List of option data dictionaries

        Returns:
            dict: Dictionary with detected spikes and their analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'Volume', 'Change in OI']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'Volume' and 'volume' in df.columns:
                    df['Volume'] = df['volume']
                elif col == 'Change in OI' and 'oiChange' in df.columns:
                    df['Change in OI'] = df['oiChange']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Volume', 'Change in OI']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate statistics for volume and OI change
        volume_mean = df['Volume'].mean()
        volume_std = df['Volume'].std() if df['Volume'].std() > 0 else volume_mean * 0.1

        oi_change_mean = df['Change in OI'].abs().mean()
        oi_change_std = df['Change in OI'].abs().std() if df['Change in OI'].abs().std() > 0 else oi_change_mean * 0.1

        # Detect spikes
        volume_spikes = df[df['Volume'] > volume_mean + (self.volume_threshold * volume_std)]

        # Separate OI increases and decreases
        oi_increases = df[(df['Change in OI'] > 0) &
                          (df['Change in OI'] > oi_change_mean + (self.oi_threshold * oi_change_std))]

        oi_decreases = df[(df['Change in OI'] < 0) &
                          (df['Change in OI'].abs() > oi_change_mean + (self.oi_threshold * oi_change_std))]

        # Find combined spikes
        fresh_positions = pd.merge(
            volume_spikes,
            oi_increases,
            on=['Strike Price', 'Option Type', 'Volume', 'Change in OI'],
            how='inner'
        )

        closing_positions = pd.merge(
            volume_spikes,
            oi_decreases,
            on=['Strike Price', 'Option Type', 'Volume', 'Change in OI'],
            how='inner'
        )

        # Prepare results
        fresh_positions_list = []
        for _, row in fresh_positions.iterrows():
            fresh_positions_list.append({
                'strike': row['Strike Price'],
                'option_type': row['Option Type'],
                'volume': row['Volume'],
                'oi_change': row['Change in OI'],
                'signal': 'Fresh Positions (Bullish)' if row['Option Type'] in ['CE', 'Call', 'C'] else 'Fresh Positions (Bearish)',
                'strength': self._calculate_spike_strength(row['Volume'], volume_mean, volume_std,
                                                         row['Change in OI'], oi_change_mean, oi_change_std)
            })

        closing_positions_list = []
        for _, row in closing_positions.iterrows():
            closing_positions_list.append({
                'strike': row['Strike Price'],
                'option_type': row['Option Type'],
                'volume': row['Volume'],
                'oi_change': row['Change in OI'],
                'signal': 'Closing Positions (Bearish)' if row['Option Type'] in ['CE', 'Call', 'C'] else 'Closing Positions (Bullish)',
                'strength': self._calculate_spike_strength(row['Volume'], volume_mean, volume_std,
                                                         abs(row['Change in OI']), oi_change_mean, oi_change_std)
            })

        # Sort by strength
        fresh_positions_list = sorted(fresh_positions_list, key=lambda x: x['strength'], reverse=True)
        closing_positions_list = sorted(closing_positions_list, key=lambda x: x['strength'], reverse=True)

        # Generate summary
        summary = self._generate_volume_oi_summary(fresh_positions_list, closing_positions_list)

        return {
            "success": True,
            "fresh_positions": fresh_positions_list,
            "closing_positions": closing_positions_list,
            "summary": summary,
            "stats": {
                "volume_mean": volume_mean,
                "volume_std": volume_std,
                "oi_change_mean": oi_change_mean,
                "oi_change_std": oi_change_std
            }
        }

    def _calculate_spike_strength(self, volume, volume_mean, volume_std, oi_change, oi_mean, oi_std):
        """Calculate the strength of a spike based on standard deviations from mean"""
        volume_z = (volume - volume_mean) / volume_std if volume_std > 0 else 0
        oi_z = (oi_change - oi_mean) / oi_std if oi_std > 0 else 0

        # Combine the z-scores with more weight on volume
        strength = (0.6 * volume_z) + (0.4 * oi_z)
        return round(strength, 2)

    def _generate_volume_oi_summary(self, fresh_positions, closing_positions):
        """Generate a summary of the volume-OI analysis"""
        if not fresh_positions and not closing_positions:
            return "No significant volume-OI spikes detected."

        summary = []

        # Analyze fresh positions
        if fresh_positions:
            bullish_count = sum(1 for p in fresh_positions if 'Bullish' in p['signal'])
            bearish_count = sum(1 for p in fresh_positions if 'Bearish' in p['signal'])

            if bullish_count > bearish_count * 2:
                summary.append("Strong bullish sentiment detected with significant fresh call buying.")
            elif bearish_count > bullish_count * 2:
                summary.append("Strong bearish sentiment detected with significant fresh put buying.")
            elif bullish_count > bearish_count:
                summary.append("Moderate bullish bias with more fresh call positions than puts.")
            elif bearish_count > bullish_count:
                summary.append("Moderate bearish bias with more fresh put positions than calls.")
            else:
                summary.append("Mixed sentiment with balanced fresh positions in both calls and puts.")

            # Highlight the strongest signal
            if fresh_positions:
                strongest = fresh_positions[0]
                summary.append(f"Most significant activity: {strongest['signal']} at strike {strongest['strike']} with volume {strongest['volume']:,} and OI change {strongest['oi_change']:,}.")

        # Analyze closing positions
        if closing_positions:
            bullish_count = sum(1 for p in closing_positions if 'Bullish' in p['signal'])
            bearish_count = sum(1 for p in closing_positions if 'Bearish' in p['signal'])

            if bullish_count > 0 or bearish_count > 0:
                summary.append("Notable position unwinding detected:")

                if bullish_count > bearish_count * 2:
                    summary.append("Strong bullish signal from put unwinding.")
                elif bearish_count > bullish_count * 2:
                    summary.append("Strong bearish signal from call unwinding.")
                elif bullish_count > bearish_count:
                    summary.append("Moderate bullish bias from more put unwinding than call unwinding.")
                elif bearish_count > bullish_count:
                    summary.append("Moderate bearish bias from more call unwinding than put unwinding.")
                else:
                    summary.append("Mixed sentiment with balanced unwinding in both calls and puts.")

        return " ".join(summary)

    def calculate_dealer_gamma(self, options_data, current_price):
        """
        Calculate dealer gamma exposure vs customer gamma

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying

        Returns:
            dict: Dictionary with dealer gamma analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'OI', 'Volume']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'OI' and 'openInterest' in df.columns:
                    df['OI'] = df['openInterest']
                elif col == 'Volume' and 'volume' in df.columns:
                    df['Volume'] = df['volume']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'OI', 'Volume']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate gamma for each option
        df['Gamma'] = df.apply(lambda row: self._calculate_option_gamma(
            current_price,
            row['Strike Price'],
            30,  # Assuming 30 days to expiry as default
            0.05,  # Risk-free rate
            0.3,  # Implied volatility (30%)
            row['Option Type'] in ['CE', 'Call', 'C']
        ), axis=1)

        # Calculate net gamma exposure at each strike
        df['Net Gamma'] = df.apply(
            lambda row: row['Gamma'] * row['OI'] * (1 if row['Option Type'] in ['CE', 'Call', 'C'] else -1),
            axis=1
        )

        # Estimate dealer vs customer gamma
        # Dealers are typically short gamma (they sell options)
        # Customers are typically long gamma (they buy options)
        df['Dealer Gamma'] = -df['Net Gamma']
        df['Customer Gamma'] = df['Net Gamma']

        # Calculate total gamma exposures
        total_gamma = df['Net Gamma'].sum()
        total_dealer_gamma = df['Dealer Gamma'].sum()
        total_customer_gamma = df['Customer Gamma'].sum()

        # Calculate gamma exposure by strike
        gamma_by_strike = df.groupby('Strike Price')['Net Gamma'].sum().reset_index()
        gamma_by_strike = gamma_by_strike.sort_values('Strike Price')

        # Find gamma flip point (where gamma changes sign)
        gamma_by_strike['Prev Gamma'] = gamma_by_strike['Net Gamma'].shift(1)
        gamma_flip_points = gamma_by_strike[
            (gamma_by_strike['Net Gamma'] * gamma_by_strike['Prev Gamma']) < 0
        ]

        # Determine dealer positioning
        dealer_positioning = "Negative Gamma (Momentum Chasing)" if total_dealer_gamma < 0 else "Positive Gamma (Selling into Rallies)"

        # Generate analysis
        analysis = self._generate_dealer_gamma_analysis(
            total_dealer_gamma,
            total_customer_gamma,
            dealer_positioning,
            gamma_flip_points,
            current_price
        )

        # Prepare gamma profile for visualization
        gamma_profile = []
        for _, row in gamma_by_strike.iterrows():
            gamma_profile.append({
                'strike': row['Strike Price'],
                'net_gamma': row['Net Gamma'],
                'dealer_gamma': -row['Net Gamma'],
                'customer_gamma': row['Net Gamma']
            })

        return {
            "success": True,
            "total_gamma": total_gamma,
            "total_dealer_gamma": total_dealer_gamma,
            "total_customer_gamma": total_customer_gamma,
            "dealer_positioning": dealer_positioning,
            "gamma_profile": gamma_profile,
            "gamma_flip_points": gamma_flip_points['Strike Price'].tolist() if not gamma_flip_points.empty else [],
            "analysis": analysis
        }

    def _calculate_option_gamma(self, spot, strike, days_to_expiry, risk_free_rate, implied_volatility, is_call):
        """
        Calculate the gamma of an option using the Black-Scholes model

        Args:
            spot (float): Current price of the underlying
            strike (float): Strike price of the option
            days_to_expiry (int): Days to expiration
            risk_free_rate (float): Risk-free interest rate
            implied_volatility (float): Implied volatility
            is_call (bool): True if call option, False if put option

        Returns:
            float: Gamma value
        """
        # Convert days to years
        t = days_to_expiry / 365.0

        # Handle expired options
        if t <= 0:
            return 0

        # Calculate d1 from Black-Scholes
        d1 = (np.log(spot / strike) + (risk_free_rate + 0.5 * implied_volatility ** 2) * t) / (implied_volatility * np.sqrt(t))

        # Calculate gamma (same for calls and puts)
        gamma = norm.pdf(d1) / (spot * implied_volatility * np.sqrt(t))

        # Scale gamma by contract multiplier (typically 100 for equity options)
        gamma = gamma * 100

        return gamma

    def _generate_dealer_gamma_analysis(self, dealer_gamma, customer_gamma, positioning, flip_points, current_price):
        """Generate analysis of dealer gamma positioning"""
        analysis = []

        # Overall positioning
        analysis.append(f"Dealer Gamma Positioning: {positioning}")

        # Magnitude of gamma
        abs_dealer_gamma = abs(dealer_gamma)
        if abs_dealer_gamma > 1000000:
            analysis.append(f"Dealers have a very large gamma position (${abs_dealer_gamma/1000000:.2f}M).")
        elif abs_dealer_gamma > 500000:
            analysis.append(f"Dealers have a significant gamma position (${abs_dealer_gamma/1000000:.2f}M).")
        elif abs_dealer_gamma > 100000:
            analysis.append(f"Dealers have a moderate gamma position (${abs_dealer_gamma/1000000:.2f}M).")
        else:
            analysis.append(f"Dealers have a small gamma position (${abs_dealer_gamma/1000:.2f}K).")

        # Implications
        if dealer_gamma < 0:
            analysis.append("With negative gamma, dealers will need to chase momentum: buying as market rises and selling as market falls.")
            analysis.append("This can amplify market moves and increase volatility.")
        else:
            analysis.append("With positive gamma, dealers will sell into rallies and buy dips.")
            analysis.append("This tends to dampen volatility and create mean-reverting price action.")

        # Flip points
        if flip_points and len(flip_points) > 0:
            closest_flip = min(flip_points, key=lambda x: abs(x - current_price))
            distance = abs(closest_flip - current_price)
            distance_pct = (distance / current_price) * 100

            analysis.append(f"Nearest gamma flip point is at strike {closest_flip}, which is {distance_pct:.1f}% {'above' if closest_flip > current_price else 'below'} current price.")
            analysis.append("Market behavior may change significantly if price crosses this level.")

        return " ".join(analysis)

    def analyze_iv_skew(self, options_data, current_price):
        """
        Analyze IV skew across strikes

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying

        Returns:
            dict: Dictionary with IV skew analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        if df['Strike Price'].dtype == 'object':
            df['Strike Price'] = pd.to_numeric(df['Strike Price'], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Extract IV from options data
        iv_column = None
        for col in ['IV', 'Implied Volatility', 'impliedVolatility']:
            if col in df.columns:
                iv_column = col
                break

        # If no IV column found, estimate IV
        if iv_column is None:
            # Add estimated IV based on option price
            df['IV'] = df.apply(lambda row: self._estimate_iv_from_price(
                row, current_price, 30  # Assuming 30 days to expiry
            ), axis=1)
            iv_column = 'IV'

        # Separate calls and puts
        calls_df = df[df['Option Type'].isin(['CE', 'Call', 'C'])]
        puts_df = df[df['Option Type'].isin(['PE', 'Put', 'P'])]

        # Calculate moneyness (K/S ratio)
        calls_df['Moneyness'] = calls_df['Strike Price'] / current_price
        puts_df['Moneyness'] = puts_df['Strike Price'] / current_price

        # Group options by moneyness categories
        deep_otm_calls = calls_df[calls_df['Moneyness'] > 1.1]
        otm_calls = calls_df[(calls_df['Moneyness'] > 1.0) & (calls_df['Moneyness'] <= 1.1)]
        atm_calls = calls_df[(calls_df['Moneyness'] >= 0.95) & (calls_df['Moneyness'] <= 1.05)]
        itm_calls = calls_df[(calls_df['Moneyness'] >= 0.9) & (calls_df['Moneyness'] < 1.0)]
        deep_itm_calls = calls_df[calls_df['Moneyness'] < 0.9]

        deep_otm_puts = puts_df[puts_df['Moneyness'] < 0.9]
        otm_puts = puts_df[(puts_df['Moneyness'] >= 0.9) & (puts_df['Moneyness'] < 1.0)]
        atm_puts = puts_df[(puts_df['Moneyness'] >= 0.95) & (puts_df['Moneyness'] <= 1.05)]
        itm_puts = puts_df[(puts_df['Moneyness'] > 1.0) & (puts_df['Moneyness'] <= 1.1)]
        deep_itm_puts = puts_df[puts_df['Moneyness'] > 1.1]

        # Calculate average IV for each category
        iv_data = {
            'deep_otm_calls': deep_otm_calls[iv_column].mean() if not deep_otm_calls.empty else 0,
            'otm_calls': otm_calls[iv_column].mean() if not otm_calls.empty else 0,
            'atm_calls': atm_calls[iv_column].mean() if not atm_calls.empty else 0,
            'itm_calls': itm_calls[iv_column].mean() if not itm_calls.empty else 0,
            'deep_itm_calls': deep_itm_calls[iv_column].mean() if not deep_itm_calls.empty else 0,
            'deep_otm_puts': deep_otm_puts[iv_column].mean() if not deep_otm_puts.empty else 0,
            'otm_puts': otm_puts[iv_column].mean() if not otm_puts.empty else 0,
            'atm_puts': atm_puts[iv_column].mean() if not atm_puts.empty else 0,
            'itm_puts': itm_puts[iv_column].mean() if not itm_puts.empty else 0,
            'deep_itm_puts': deep_itm_puts[iv_column].mean() if not deep_itm_puts.empty else 0
        }

        # Calculate IV skew metrics
        iv_skew = {
            'call_skew': iv_data['otm_calls'] - iv_data['atm_calls'] if iv_data['atm_calls'] > 0 else 0,
            'put_skew': iv_data['otm_puts'] - iv_data['atm_puts'] if iv_data['atm_puts'] > 0 else 0,
            'wings_skew': (iv_data['deep_otm_calls'] + iv_data['deep_otm_puts']) / 2 - iv_data['atm_calls']
                if iv_data['atm_calls'] > 0 else 0,
            'put_call_skew': iv_data['otm_puts'] - iv_data['otm_calls']
        }

        # Calculate IV surface for visualization
        iv_surface = []
        for _, row in df.iterrows():
            moneyness = row['Strike Price'] / current_price
            iv_surface.append({
                'strike': row['Strike Price'],
                'moneyness': moneyness,
                'iv': row[iv_column],
                'option_type': 'Call' if row['Option Type'] in ['CE', 'Call', 'C'] else 'Put'
            })

        # Generate analysis
        analysis = self._generate_iv_skew_analysis(iv_data, iv_skew)

        return {
            "success": True,
            "iv_data": iv_data,
            "iv_skew": iv_skew,
            "iv_surface": iv_surface,
            "analysis": analysis
        }

    def _estimate_iv_from_price(self, option_row, current_price, days_to_expiry):
        """Estimate implied volatility from option price using a simple heuristic"""
        try:
            option_type = option_row['Option Type']
            strike = option_row['Strike Price']

            # Get option price
            price = 0
            for price_col in ['LTP', 'lastPrice', 'Last Price']:
                if price_col in option_row and option_row[price_col] > 0:
                    price = option_row[price_col]
                    break

            if price <= 0:
                return 0.3  # Default 30% IV

            # Calculate moneyness
            moneyness = strike / current_price

            # Simple IV estimation based on moneyness and price/strike ratio
            price_to_strike_ratio = price / strike

            # Base IV estimate
            if option_type in ['CE', 'Call', 'C']:  # Call option
                if moneyness > 1.1:  # Deep OTM
                    base_iv = 0.4  # Higher IV for OTM options
                elif moneyness < 0.9:  # Deep ITM
                    base_iv = 0.25  # Lower IV for ITM options
                else:  # Near the money
                    base_iv = 0.3
            else:  # Put option
                if moneyness < 0.9:  # Deep OTM
                    base_iv = 0.4  # Higher IV for OTM options
                elif moneyness > 1.1:  # Deep ITM
                    base_iv = 0.25  # Lower IV for ITM options
                else:  # Near the money
                    base_iv = 0.3

            # Adjust based on price/strike ratio
            if price_to_strike_ratio > 0.1:
                iv_adjustment = 0.05
            elif price_to_strike_ratio > 0.05:
                iv_adjustment = 0.02
            elif price_to_strike_ratio < 0.01:
                iv_adjustment = -0.05
            else:
                iv_adjustment = 0

            return base_iv + iv_adjustment

        except Exception as e:
            print(f"Error estimating IV: {e}")
            return 0.3  # Default 30% IV

    def _generate_iv_skew_analysis(self, iv_data, iv_skew):
        """Generate analysis of IV skew"""
        analysis = []

        # Overall IV level
        atm_iv = (iv_data['atm_calls'] + iv_data['atm_puts']) / 2
        analysis.append(f"ATM Implied Volatility: {atm_iv:.1f}%")

        # Call skew analysis
        if iv_skew['call_skew'] > 5:
            analysis.append("Strong positive call skew indicates expectations of upside movement or tail risk.")
        elif iv_skew['call_skew'] > 2:
            analysis.append("Moderate positive call skew suggests some bullish sentiment.")
        elif iv_skew['call_skew'] < -2:
            analysis.append("Negative call skew indicates limited expectations of upside movement.")

        # Put skew analysis
        if iv_skew['put_skew'] > 5:
            analysis.append("Strong positive put skew indicates significant downside protection buying or fear.")
        elif iv_skew['put_skew'] > 2:
            analysis.append("Moderate positive put skew suggests defensive positioning.")
        elif iv_skew['put_skew'] < -2:
            analysis.append("Negative put skew is unusual and may indicate complacency about downside risks.")

        # Put-call skew comparison
        if iv_skew['put_call_skew'] > 5:
            analysis.append("Put premium significantly exceeds call premium, indicating bearish sentiment or hedging.")
        elif iv_skew['put_call_skew'] > 2:
            analysis.append("Put premium exceeds call premium, suggesting cautious positioning.")
        elif iv_skew['put_call_skew'] < -2:
            analysis.append("Call premium exceeds put premium, indicating bullish sentiment or call buying activity.")
        else:
            analysis.append("Balanced put-call skew suggests neutral market expectations.")

        # Wings skew (tail risk)
        if iv_skew['wings_skew'] > 7:
            analysis.append("Elevated wings skew indicates significant tail risk concerns in both directions.")
        elif iv_skew['wings_skew'] > 3:
            analysis.append("Moderate wings skew suggests some concern about extreme market moves.")

        # Trading implications
        if iv_skew['put_skew'] > 3 and iv_skew['call_skew'] < 1:
            analysis.append("Consider strategies that sell expensive puts and buy cheaper calls.")
        elif iv_skew['call_skew'] > 3 and iv_skew['put_skew'] < 1:
            analysis.append("Consider strategies that sell expensive calls and buy cheaper puts.")
        elif iv_skew['wings_skew'] > 5:
            analysis.append("Consider iron condor strategies to sell expensive wings.")

        return " ".join(analysis)

    def calculate_delta_weighted_oi(self, options_data, current_price):
        """
        Calculate delta-weighted open interest

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying

        Returns:
            dict: Dictionary with delta-weighted OI analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'OI']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'OI' and 'openInterest' in df.columns:
                    df['OI'] = df['openInterest']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'OI']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate delta for each option
        df['Delta'] = df.apply(lambda row: self._calculate_option_delta(
            current_price,
            row['Strike Price'],
            30,  # Assuming 30 days to expiry as default
            0.05,  # Risk-free rate
            0.3,  # Implied volatility (30%)
            row['Option Type'] in ['CE', 'Call', 'C']
        ), axis=1)

        # Calculate delta-weighted OI
        df['Delta_Weighted_OI'] = df.apply(
            lambda row: row['Delta'] * row['OI'] * (1 if row['Option Type'] in ['CE', 'Call', 'C'] else -1),
            axis=1
        )

        # Calculate total delta-weighted OI
        total_delta_weighted_oi = df['Delta_Weighted_OI'].sum()

        # Calculate delta-weighted OI by strike
        delta_weighted_oi_by_strike = df.groupby('Strike Price')['Delta_Weighted_OI'].sum().reset_index()
        delta_weighted_oi_by_strike = delta_weighted_oi_by_strike.sort_values('Strike Price')

        # Find concentration points (strikes with highest absolute delta-weighted OI)
        concentration_points = delta_weighted_oi_by_strike.copy()
        concentration_points['Abs_Delta_Weighted_OI'] = concentration_points['Delta_Weighted_OI'].abs()
        concentration_points = concentration_points.sort_values('Abs_Delta_Weighted_OI', ascending=False).head(5)

        # Calculate cumulative delta-weighted OI
        delta_weighted_oi_by_strike['Cumulative_Delta_Weighted_OI'] = delta_weighted_oi_by_strike['Delta_Weighted_OI'].cumsum()

        # Find zero-cross points (where cumulative delta-weighted OI changes sign)
        delta_weighted_oi_by_strike['Prev_Cum_Delta_OI'] = delta_weighted_oi_by_strike['Cumulative_Delta_Weighted_OI'].shift(1)
        zero_cross_points = delta_weighted_oi_by_strike[
            (delta_weighted_oi_by_strike['Cumulative_Delta_Weighted_OI'] * delta_weighted_oi_by_strike['Prev_Cum_Delta_OI']) < 0
        ]

        # Prepare delta-weighted OI profile for visualization
        delta_weighted_oi_profile = []
        for _, row in delta_weighted_oi_by_strike.iterrows():
            delta_weighted_oi_profile.append({
                'strike': row['Strike Price'],
                'delta_weighted_oi': row['Delta_Weighted_OI'],
                'cumulative_delta_weighted_oi': row['Cumulative_Delta_Weighted_OI']
            })

        # Generate analysis
        analysis = self._generate_delta_weighted_oi_analysis(
            total_delta_weighted_oi,
            concentration_points,
            zero_cross_points,
            current_price
        )

        return {
            "success": True,
            "total_delta_weighted_oi": total_delta_weighted_oi,
            "delta_weighted_oi_profile": delta_weighted_oi_profile,
            "concentration_points": concentration_points[['Strike Price', 'Delta_Weighted_OI']].to_dict('records'),
            "zero_cross_points": zero_cross_points['Strike Price'].tolist() if not zero_cross_points.empty else [],
            "analysis": analysis
        }

    def _calculate_option_delta(self, spot, strike, days_to_expiry, risk_free_rate, implied_volatility, is_call):
        """
        Calculate the delta of an option using the Black-Scholes model

        Args:
            spot (float): Current price of the underlying
            strike (float): Strike price of the option
            days_to_expiry (int): Days to expiration
            risk_free_rate (float): Risk-free interest rate
            implied_volatility (float): Implied volatility
            is_call (bool): True if call option, False if put option

        Returns:
            float: Delta value
        """
        # Convert days to years
        t = days_to_expiry / 365.0

        # Handle expired options
        if t <= 0:
            return 1.0 if is_call and spot > strike else 0.0

        # Calculate d1 from Black-Scholes
        d1 = (np.log(spot / strike) + (risk_free_rate + 0.5 * implied_volatility ** 2) * t) / (implied_volatility * np.sqrt(t))

        # Calculate delta
        if is_call:
            delta = norm.cdf(d1)
        else:
            delta = norm.cdf(d1) - 1

        return delta

    def _generate_delta_weighted_oi_analysis(self, total_delta_weighted_oi, concentration_points, zero_cross_points, current_price):
        """Generate analysis of delta-weighted OI positioning"""
        analysis = []

        # Overall positioning
        if total_delta_weighted_oi > 0:
            analysis.append(f"Net Delta-Weighted OI is positive ({total_delta_weighted_oi:,.0f}), indicating bullish positioning.")
        elif total_delta_weighted_oi < 0:
            analysis.append(f"Net Delta-Weighted OI is negative ({total_delta_weighted_oi:,.0f}), indicating bearish positioning.")
        else:
            analysis.append("Net Delta-Weighted OI is neutral, indicating balanced positioning.")

        # Magnitude of positioning
        abs_delta_weighted_oi = abs(total_delta_weighted_oi)
        if abs_delta_weighted_oi > 1000000:
            analysis.append(f"The magnitude is very large ({abs_delta_weighted_oi/1000000:.2f}M), suggesting strong directional bias.")
        elif abs_delta_weighted_oi > 500000:
            analysis.append(f"The magnitude is significant ({abs_delta_weighted_oi/1000000:.2f}M), suggesting clear directional bias.")
        elif abs_delta_weighted_oi > 100000:
            analysis.append(f"The magnitude is moderate ({abs_delta_weighted_oi/1000:.2f}K), suggesting some directional bias.")
        else:
            analysis.append(f"The magnitude is small ({abs_delta_weighted_oi/1000:.2f}K), suggesting limited directional bias.")

        # Concentration points
        if not concentration_points.empty:
            top_concentration = concentration_points.iloc[0]
            strike = top_concentration['Strike Price']
            delta_oi = top_concentration['Delta_Weighted_OI']

            analysis.append(f"Highest delta-weighted OI concentration is at strike {strike}, with a value of {delta_oi:,.0f}.")

            if delta_oi > 0:
                analysis.append(f"Strong bullish positioning at strike {strike}.")
            else:
                analysis.append(f"Strong bearish positioning at strike {strike}.")

            # Check if concentration is near current price
            distance = abs(strike - current_price)
            distance_pct = (distance / current_price) * 100

            if distance_pct < 2:
                analysis.append(f"This concentration is very close to the current price, suggesting significant interest at current levels.")
            elif distance_pct < 5:
                analysis.append(f"This concentration is near the current price, suggesting interest around current levels.")

        # Zero-cross points
        if zero_cross_points is not None and len(zero_cross_points) > 0:
            closest_zero_cross = min(zero_cross_points, key=lambda x: abs(x - current_price))
            distance = abs(closest_zero_cross - current_price)
            distance_pct = (distance / current_price) * 100

            analysis.append(f"Nearest delta-weighted OI zero-cross point is at strike {closest_zero_cross}, which is {distance_pct:.1f}% {'above' if closest_zero_cross > current_price else 'below'} current price.")
            analysis.append("This level may act as a significant support/resistance level.")

        return " ".join(analysis)

    def analyze_liquidity_depth(self, options_data):
        """
        Analyze liquidity depth at each strike

        Args:
            options_data (list): List of option data dictionaries

        Returns:
            dict: Dictionary with liquidity depth analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'Volume', 'Bid', 'Ask']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'Volume' and 'volume' in df.columns:
                    df['Volume'] = df['volume']
                elif col == 'Bid' and 'bidPrice' in df.columns:
                    df['Bid'] = df['bidPrice']
                elif col == 'Ask' and 'askPrice' in df.columns:
                    df['Ask'] = df['askPrice']
                else:
                    # If we're missing bid/ask, we can still proceed with just volume
                    if col in ['Bid', 'Ask']:
                        df[col] = 0
                    else:
                        return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'Volume', 'Bid', 'Ask']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate bid-ask spread
        df['Spread'] = df['Ask'] - df['Bid']
        df['Spread_Pct'] = (df['Spread'] / ((df['Bid'] + df['Ask']) / 2)) * 100

        # Calculate liquidity metrics
        df['Liquidity_Score'] = df.apply(self._calculate_liquidity_score, axis=1)

        # Aggregate liquidity by strike
        liquidity_by_strike = df.groupby('Strike Price').agg({
            'Volume': 'sum',
            'Spread_Pct': 'mean',
            'Liquidity_Score': 'mean'
        }).reset_index()

        # Sort by strike price
        liquidity_by_strike = liquidity_by_strike.sort_values('Strike Price')

        # Find most liquid strikes (highest volume, lowest spread)
        most_liquid_strikes = df.sort_values('Liquidity_Score', ascending=False).head(5)

        # Prepare liquidity profile for visualization
        liquidity_profile = []
        for _, row in liquidity_by_strike.iterrows():
            liquidity_profile.append({
                'strike': row['Strike Price'],
                'volume': row['Volume'],
                'spread_pct': row['Spread_Pct'],
                'liquidity_score': row['Liquidity_Score']
            })

        # Generate analysis
        analysis = self._generate_liquidity_analysis(liquidity_by_strike, most_liquid_strikes)

        return {
            "success": True,
            "liquidity_profile": liquidity_profile,
            "most_liquid_strikes": most_liquid_strikes[['Strike Price', 'Option Type', 'Volume', 'Spread_Pct', 'Liquidity_Score']].to_dict('records'),
            "analysis": analysis
        }

    def _calculate_liquidity_score(self, row):
        """Calculate a liquidity score based on volume and spread"""
        volume = row['Volume']
        spread_pct = row['Spread_Pct']

        # Avoid division by zero
        if spread_pct <= 0:
            spread_pct = 0.01

        # Higher volume and lower spread = higher liquidity
        # Scale volume logarithmically to avoid extreme values dominating
        volume_factor = np.log1p(volume) / 10 if volume > 0 else 0
        spread_factor = 100 / spread_pct if spread_pct > 0 else 100

        # Combine factors with more weight on spread for options
        liquidity_score = (0.4 * volume_factor) + (0.6 * spread_factor)

        return liquidity_score

    def _generate_liquidity_analysis(self, liquidity_by_strike, most_liquid_strikes):
        """Generate analysis of liquidity depth"""
        analysis = []

        # Overall liquidity assessment
        avg_liquidity = liquidity_by_strike['Liquidity_Score'].mean()
        if avg_liquidity > 50:
            analysis.append("Overall liquidity is excellent across the option chain.")
        elif avg_liquidity > 30:
            analysis.append("Overall liquidity is good across the option chain.")
        elif avg_liquidity > 15:
            analysis.append("Overall liquidity is moderate across the option chain.")
        else:
            analysis.append("Overall liquidity is poor across the option chain.")

        # Most liquid strikes
        if not most_liquid_strikes.empty:
            top_liquid = most_liquid_strikes.iloc[0]
            analysis.append(f"Most liquid option: {top_liquid['Option Type']} at strike {top_liquid['Strike Price']} with volume {top_liquid['Volume']:,.0f} and spread {top_liquid['Spread_Pct']:.2f}%.")

            # Analyze liquid strikes distribution
            call_count = sum(1 for _, row in most_liquid_strikes.iterrows() if row['Option Type'] in ['CE', 'Call', 'C'])
            put_count = sum(1 for _, row in most_liquid_strikes.iterrows() if row['Option Type'] in ['PE', 'Put', 'P'])

            if call_count > put_count * 2:
                analysis.append("Liquidity is heavily concentrated in call options, suggesting bullish sentiment or hedging activity.")
            elif put_count > call_count * 2:
                analysis.append("Liquidity is heavily concentrated in put options, suggesting bearish sentiment or protective positioning.")
            else:
                analysis.append("Liquidity is relatively balanced between calls and puts.")

        # Liquidity distribution
        liquidity_std = liquidity_by_strike['Liquidity_Score'].std()
        if liquidity_std > 20:
            analysis.append("Liquidity is unevenly distributed across strikes, with some strikes much more liquid than others.")
        else:
            analysis.append("Liquidity is relatively evenly distributed across strikes.")

        # Trading implications
        analysis.append("For optimal execution, focus on trading the most liquid strikes to minimize slippage and impact costs.")

        if avg_liquidity < 20:
            analysis.append("Consider using limit orders and avoiding market orders due to limited liquidity.")

        return " ".join(analysis)

    def detect_smart_money(self, options_data):
        """
        Detect smart money activity through block trades and unusual activity

        Args:
            options_data (list): List of option data dictionaries

        Returns:
            dict: Dictionary with smart money analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'Volume', 'OI', 'Change in OI']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'Volume' and 'volume' in df.columns:
                    df['Volume'] = df['volume']
                elif col == 'OI' and 'openInterest' in df.columns:
                    df['OI'] = df['openInterest']
                elif col == 'Change in OI' and 'oiChange' in df.columns:
                    df['Change in OI'] = df['oiChange']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'Volume', 'OI', 'Change in OI']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate volume to OI ratio (unusual activity indicator)
        df['Volume_OI_Ratio'] = df.apply(
            lambda row: row['Volume'] / row['OI'] if row['OI'] > 0 else 0,
            axis=1
        )

        # Calculate average volume per strike
        avg_volume_by_strike = df.groupby('Strike Price')['Volume'].mean().reset_index()
        avg_volume_by_strike.columns = ['Strike Price', 'Avg_Volume']

        # Merge back to main dataframe
        df = pd.merge(df, avg_volume_by_strike, on='Strike Price', how='left')

        # Calculate volume anomaly (how much current volume exceeds average)
        df['Volume_Anomaly'] = df['Volume'] / df['Avg_Volume'] if df['Avg_Volume'].mean() > 0 else df['Volume']

        # Detect block trades (large volume trades)
        volume_threshold = df['Volume'].mean() + (2 * df['Volume'].std())
        block_trades = df[df['Volume'] > max(volume_threshold, 100)]

        # Detect unusual activity (high volume relative to OI)
        unusual_activity = df[df['Volume_OI_Ratio'] > 0.5]

        # Calculate smart money score
        df['Smart_Money_Score'] = df.apply(self._calculate_smart_money_score, axis=1)

        # Find options with highest smart money scores
        smart_money_options = df.sort_values('Smart_Money_Score', ascending=False).head(5)

        # Generate analysis
        analysis = self._generate_smart_money_analysis(smart_money_options, block_trades, unusual_activity)

        return {
            "success": True,
            "smart_money_options": smart_money_options[['Strike Price', 'Option Type', 'Volume', 'OI', 'Change in OI', 'Smart_Money_Score']].to_dict('records'),
            "block_trades": block_trades[['Strike Price', 'Option Type', 'Volume', 'OI']].to_dict('records') if not block_trades.empty else [],
            "unusual_activity": unusual_activity[['Strike Price', 'Option Type', 'Volume', 'OI', 'Volume_OI_Ratio']].to_dict('records') if not unusual_activity.empty else [],
            "analysis": analysis
        }

    def _calculate_smart_money_score(self, row):
        """Calculate a smart money score based on volume, OI, and OI change"""
        volume = row['Volume']
        oi = row['OI']
        oi_change = row['Change in OI']
        volume_anomaly = row['Volume_Anomaly']

        # Avoid division by zero
        if oi <= 0:
            oi = 1

        # Components of the smart money score
        volume_factor = np.log1p(volume) / 10
        volume_anomaly_factor = np.log1p(volume_anomaly) if volume_anomaly > 1 else 0
        oi_change_factor = abs(oi_change) / oi if oi > 0 else 0

        # Combine factors
        smart_money_score = (0.4 * volume_factor) + (0.4 * volume_anomaly_factor) + (0.2 * oi_change_factor)

        # Boost score if volume is high and OI is increasing (fresh positions)
        if volume > 100 and oi_change > 0:
            smart_money_score *= 1.5

        return smart_money_score

    def _generate_smart_money_analysis(self, smart_money_options, block_trades, unusual_activity):
        """Generate analysis of smart money activity"""
        analysis = []

        # Check if we have any smart money activity
        if (smart_money_options is None or smart_money_options.empty) and (block_trades is None or block_trades.empty) and (unusual_activity is None or unusual_activity.empty):
            analysis.append("No significant smart money activity detected in the current option chain.")
            return " ".join(analysis)

        # Analyze smart money options
        if smart_money_options is not None and not smart_money_options.empty:
            top_option = smart_money_options.iloc[0]
            option_type = top_option['Option Type']
            strike = top_option['Strike Price']
            volume = top_option['Volume']
            oi_change = top_option['Change in OI']

            analysis.append(f"Highest smart money activity detected in {option_type} at strike {strike}.")
            analysis.append(f"Volume: {volume:,.0f}, OI Change: {oi_change:,.0f}")

            # Analyze directional bias
            call_count = sum(1 for _, row in smart_money_options.iterrows() if row['Option Type'] in ['CE', 'Call', 'C'])
            put_count = sum(1 for _, row in smart_money_options.iterrows() if row['Option Type'] in ['PE', 'Put', 'P'])

            if call_count > put_count * 2:
                analysis.append("Smart money activity is heavily concentrated in call options, suggesting bullish positioning.")
            elif put_count > call_count * 2:
                analysis.append("Smart money activity is heavily concentrated in put options, suggesting bearish positioning.")
            else:
                analysis.append("Smart money activity is relatively balanced between calls and puts.")

        # Analyze block trades
        if block_trades is not None and not block_trades.empty:
            block_count = len(block_trades)
            avg_block_size = block_trades['Volume'].mean()

            analysis.append(f"Detected {block_count} block trades with average size of {avg_block_size:,.0f} contracts.")

            # Largest block trade
            largest_block = block_trades.sort_values('Volume', ascending=False).iloc[0]
            analysis.append(f"Largest block trade: {largest_block['Option Type']} at strike {largest_block['Strike Price']} with volume {largest_block['Volume']:,.0f}.")

            # Block trade distribution
            call_blocks = block_trades[block_trades['Option Type'].isin(['CE', 'Call', 'C'])]
            put_blocks = block_trades[block_trades['Option Type'].isin(['PE', 'Put', 'P'])]

            if len(call_blocks) > len(put_blocks) * 2:
                analysis.append("Block trades are predominantly in call options, suggesting bullish institutional positioning.")
            elif len(put_blocks) > len(call_blocks) * 2:
                analysis.append("Block trades are predominantly in put options, suggesting bearish institutional positioning.")

        # Analyze unusual activity
        if unusual_activity is not None and not unusual_activity.empty:
            unusual_count = len(unusual_activity)
            analysis.append(f"Detected {unusual_count} instances of unusual options activity (high volume relative to open interest).")

            # Highest volume/OI ratio
            highest_ratio = unusual_activity.sort_values('Volume_OI_Ratio', ascending=False).iloc[0]
            analysis.append(f"Most unusual activity: {highest_ratio['Option Type']} at strike {highest_ratio['Strike Price']} with volume/OI ratio of {highest_ratio['Volume_OI_Ratio']:.2f}.")

        # Trading implications
        if smart_money_options is not None and not smart_money_options.empty:
            top_option = smart_money_options.iloc[0]
            option_type = top_option['Option Type']
            strike = top_option['Strike Price']

            if option_type in ['CE', 'Call', 'C']:
                analysis.append(f"Consider following smart money with bullish strategies targeting strike {strike}.")
            else:
                analysis.append(f"Consider following smart money with bearish strategies targeting strike {strike}.")

        return " ".join(analysis)

    def track_max_pain_shift(self, options_data, current_price, historical_data=None):
        """
        Track and analyze max pain shifts over time

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            historical_data (dict): Historical max pain data

        Returns:
            dict: Dictionary with max pain shift analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'OI']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'OI' and 'openInterest' in df.columns:
                    df['OI'] = df['openInterest']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'OI']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate max pain
        max_pain_strike = self._calculate_max_pain(df)

        # Initialize historical data if not provided
        if historical_data is None:
            historical_data = {
                'dates': [],
                'values': [],
                'price': []
            }

        # Get current date
        current_date = datetime.now().strftime('%Y-%m-%d')

        # Check if we already have data for today
        if current_date in historical_data['dates']:
            # Update today's data
            idx = historical_data['dates'].index(current_date)
            historical_data['values'][idx] = max_pain_strike
            historical_data['price'][idx] = current_price
        else:
            # Add new data point
            historical_data['dates'].append(current_date)
            historical_data['values'].append(max_pain_strike)
            historical_data['price'].append(current_price)

        # Keep only the last 7 days of data
        if len(historical_data['dates']) > 7:
            historical_data['dates'] = historical_data['dates'][-7:]
            historical_data['values'] = historical_data['values'][-7:]
            historical_data['price'] = historical_data['price'][-7:]

        # Calculate max pain shifts
        max_pain_shift = self._calculate_max_pain_shift(historical_data)

        # Generate analysis
        analysis = self._generate_max_pain_shift_analysis(max_pain_strike, max_pain_shift, current_price, historical_data)

        return {
            "success": True,
            "current_max_pain": max_pain_strike,
            "max_pain_shift": max_pain_shift,
            "historical_max_pain": historical_data,
            "analysis": analysis
        }

    def _calculate_max_pain(self, df):
        """Calculate max pain strike price"""
        # Get unique strike prices
        strikes = df['Strike Price'].unique()
        strikes.sort()

        # Calculate pain at each strike
        pain_by_strike = []
        for strike in strikes:
            # Calculate pain for calls
            call_pain = 0
            call_df = df[df['Option Type'].isin(['CE', 'Call', 'C'])]
            for _, row in call_df.iterrows():
                option_strike = row['Strike Price']
                oi = row['OI']
                # Pain = OI * max(0, strike - option_strike)
                pain = oi * max(0, strike - option_strike)
                call_pain += pain

            # Calculate pain for puts
            put_pain = 0
            put_df = df[df['Option Type'].isin(['PE', 'Put', 'P'])]
            for _, row in put_df.iterrows():
                option_strike = row['Strike Price']
                oi = row['OI']
                # Pain = OI * max(0, option_strike - strike)
                pain = oi * max(0, option_strike - strike)
                put_pain += pain

            # Total pain at this strike
            total_pain = call_pain + put_pain
            pain_by_strike.append((strike, total_pain))

        # Find strike with minimum pain
        min_pain_strike = min(pain_by_strike, key=lambda x: x[1])[0]
        return min_pain_strike

    def _calculate_max_pain_shift(self, historical_data):
        """Calculate max pain shifts over time"""
        values = historical_data['values']

        if len(values) < 2:
            return {
                'daily_shift': "N/A",
                'weekly_shift': "N/A",
                'trend': "N/A",
                'acceleration': "N/A"
            }

        # Calculate daily shift
        daily_shift = values[-1] - values[-2]

        # Calculate weekly shift if we have enough data
        weekly_shift = values[-1] - values[0] if len(values) >= 7 else "N/A"

        # Determine trend
        if len(values) >= 3:
            recent_shifts = [values[i] - values[i-1] for i in range(1, len(values))]
            positive_shifts = sum(1 for shift in recent_shifts if shift > 0)
            negative_shifts = sum(1 for shift in recent_shifts if shift < 0)

            if positive_shifts > negative_shifts * 2:
                trend = "Strongly Upward"
            elif positive_shifts > negative_shifts:
                trend = "Upward"
            elif negative_shifts > positive_shifts * 2:
                trend = "Strongly Downward"
            elif negative_shifts > positive_shifts:
                trend = "Downward"
            else:
                trend = "Sideways"
        else:
            trend = "Insufficient Data"

        # Determine acceleration
        if len(values) >= 3:
            shifts = [values[i] - values[i-1] for i in range(1, len(values))]
            shift_changes = [shifts[i] - shifts[i-1] for i in range(1, len(shifts))]

            if len(shift_changes) > 0:
                avg_change = sum(shift_changes) / len(shift_changes)

                if avg_change > 10:
                    acceleration = "Strong"
                elif avg_change > 0:
                    acceleration = "Moderate"
                elif avg_change < -10:
                    acceleration = "Strong Deceleration"
                elif avg_change < 0:
                    acceleration = "Moderate Deceleration"
                else:
                    acceleration = "Stable"
            else:
                acceleration = "Insufficient Data"
        else:
            acceleration = "Insufficient Data"

        return {
            'daily_shift': f"+{daily_shift}" if daily_shift > 0 else f"{daily_shift}",
            'weekly_shift': f"+{weekly_shift}" if isinstance(weekly_shift, (int, float)) and weekly_shift > 0 else f"{weekly_shift}",
            'trend': trend,
            'acceleration': acceleration
        }

    def _generate_max_pain_shift_analysis(self, max_pain_strike, max_pain_shift, current_price, historical_data):
        """Generate analysis of max pain shifts"""
        analysis = []

        # Current max pain
        analysis.append(f"Current max pain is at strike {max_pain_strike}.")

        # Distance from current price
        distance = abs(max_pain_strike - current_price)
        distance_pct = (distance / current_price) * 100

        if distance_pct < 1:
            analysis.append(f"Max pain is very close to current price (within 1%), suggesting potential price stability around this level.")
        elif distance_pct < 3:
            analysis.append(f"Max pain is near current price ({distance_pct:.1f}% {'above' if max_pain_strike > current_price else 'below'}), suggesting potential price movement toward this level.")
        else:
            analysis.append(f"Max pain is {distance_pct:.1f}% {'above' if max_pain_strike > current_price else 'below'} current price.")

        # Max pain shifts
        if max_pain_shift['daily_shift'] != "N/A":
            analysis.append(f"Max pain shifted {max_pain_shift['daily_shift']} points since yesterday.")

        if max_pain_shift['weekly_shift'] != "N/A":
            analysis.append(f"Max pain shifted {max_pain_shift['weekly_shift']} points over the past week.")

        if max_pain_shift['trend'] != "N/A" and max_pain_shift['trend'] != "Insufficient Data":
            analysis.append(f"The trend in max pain is {max_pain_shift['trend'].lower()}.")

        if max_pain_shift['acceleration'] != "N/A" and max_pain_shift['acceleration'] != "Insufficient Data":
            analysis.append(f"Max pain shift is {max_pain_shift['acceleration'].lower()}.")

        # Historical relationship between price and max pain
        if len(historical_data['dates']) >= 3:
            # Check if price tends to move toward max pain
            price_moves_to_pain = 0
            price_moves_away = 0

            for i in range(1, len(historical_data['dates'])):
                prev_price = historical_data['price'][i-1]
                prev_pain = historical_data['values'][i-1]
                curr_price = historical_data['price'][i]

                prev_distance = abs(prev_price - prev_pain)
                curr_distance = abs(curr_price - prev_pain)

                if curr_distance < prev_distance:
                    price_moves_to_pain += 1
                else:
                    price_moves_away += 1

            if price_moves_to_pain > price_moves_away * 2:
                analysis.append("Historically, price has shown a strong tendency to move toward max pain levels.")
            elif price_moves_to_pain > price_moves_away:
                analysis.append("Historically, price has shown some tendency to move toward max pain levels.")

        return " ".join(analysis)

    def track_max_pain_shift(self, options_data, current_price, historical_max_pain=None):
        """
        Track and analyze max pain shifts over time

        Args:
            options_data (list): List of option data dictionaries
            current_price (float): Current price of the underlying
            historical_max_pain (dict, optional): Historical max pain values. Defaults to None.

        Returns:
            dict: Dictionary with max pain shift analysis
        """
        if not options_data or len(options_data) == 0:
            return {"success": False, "message": "No options data provided"}

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(options_data)

        # Ensure we have the necessary columns
        required_cols = ['Strike Price', 'Option Type', 'OI']
        for col in required_cols:
            if col not in df.columns:
                # Try alternative column names
                if col == 'Strike Price' and 'strikePrice' in df.columns:
                    df['Strike Price'] = df['strikePrice']
                elif col == 'Option Type' and 'optionType' in df.columns:
                    df['Option Type'] = df['optionType']
                elif col == 'OI' and 'openInterest' in df.columns:
                    df['OI'] = df['openInterest']
                else:
                    return {"success": False, "message": f"Missing required column: {col}"}

        # Convert to numeric if needed
        for col in ['Strike Price', 'OI']:
            if df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Replace NaN with 0
        df = df.fillna(0)

        # Calculate max pain
        current_max_pain = self._calculate_max_pain(df)

        # Initialize historical data if not provided
        if historical_max_pain is None:
            historical_max_pain = {
                'dates': [],
                'values': [],
                'price': []
            }

        # Add current max pain to historical data
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')

        # Only add if we don't already have an entry for today
        if today not in historical_max_pain['dates']:
            historical_max_pain['dates'].append(today)
            historical_max_pain['values'].append(current_max_pain)
            historical_max_pain['price'].append(current_price)

        # Calculate max pain shift metrics
        max_pain_shift = self._calculate_max_pain_shift(historical_max_pain)

        # Generate analysis
        analysis = self._generate_max_pain_shift_analysis(current_max_pain, max_pain_shift, current_price, historical_max_pain)

        return {
            "success": True,
            "current_max_pain": current_max_pain,
            "historical_max_pain": historical_max_pain,
            "max_pain_shift": max_pain_shift,
            "analysis": analysis
        }

    def _calculate_max_pain(self, df):
        """Calculate max pain (strike price where option writers have minimum pain)"""
        # Get unique strike prices
        strikes = sorted(df['Strike Price'].unique())

        # Calculate pain for each strike
        pain_by_strike = []

        for strike in strikes:
            # Calculate pain for call options
            call_pain = 0
            for _, call_row in df[df['Option Type'].isin(['CE', 'Call', 'C'])].iterrows():
                call_strike = call_row['Strike Price']
                call_oi = call_row['OI']

                # Call option is ITM if strike < potential spot (current strike being tested)
                if call_strike < strike:
                    # Pain = OI * (spot - strike) for calls
                    call_pain += call_oi * (strike - call_strike)

            # Calculate pain for put options
            put_pain = 0
            for _, put_row in df[df['Option Type'].isin(['PE', 'Put', 'P'])].iterrows():
                put_strike = put_row['Strike Price']
                put_oi = put_row['OI']

                # Put option is ITM if strike > potential spot (current strike being tested)
                if put_strike > strike:
                    # Pain = OI * (strike - spot) for puts
                    put_pain += put_oi * (put_strike - strike)

            # Total pain at this strike
            total_pain = call_pain + put_pain
            pain_by_strike.append((strike, total_pain))

        # Find strike with minimum pain
        if pain_by_strike:
            max_pain_strike = min(pain_by_strike, key=lambda x: x[1])[0]
            return max_pain_strike
        else:
            return 0

    def _calculate_max_pain_shift(self, historical_max_pain):
        """Calculate metrics related to max pain shifts over time"""
        if len(historical_max_pain['values']) < 2:
            return {
                'daily_shift': 0,
                'weekly_shift': 0,
                'trend': 'Neutral',
                'acceleration': 0
            }

        # Calculate daily shift (most recent change)
        daily_shift = historical_max_pain['values'][-1] - historical_max_pain['values'][-2]

        # Calculate weekly shift (if we have enough data)
        weekly_shift = 0
        if len(historical_max_pain['values']) >= 5:
            weekly_shift = historical_max_pain['values'][-1] - historical_max_pain['values'][-5]

        # Determine trend
        if len(historical_max_pain['values']) >= 3:
            recent_shifts = [
                historical_max_pain['values'][i] - historical_max_pain['values'][i-1]
                for i in range(len(historical_max_pain['values'])-1, max(0, len(historical_max_pain['values'])-4), -1)
            ]

            if all(shift > 0 for shift in recent_shifts):
                trend = 'Strong Upward'
            elif all(shift < 0 for shift in recent_shifts):
                trend = 'Strong Downward'
            elif sum(1 for shift in recent_shifts if shift > 0) > sum(1 for shift in recent_shifts if shift < 0):
                trend = 'Moderate Upward'
            elif sum(1 for shift in recent_shifts if shift < 0) > sum(1 for shift in recent_shifts if shift > 0):
                trend = 'Moderate Downward'
            else:
                trend = 'Neutral'
        else:
            trend = 'Neutral'

        # Calculate acceleration (change in the rate of change)
        acceleration = 0
        if len(historical_max_pain['values']) >= 3:
            last_shift = historical_max_pain['values'][-1] - historical_max_pain['values'][-2]
            previous_shift = historical_max_pain['values'][-2] - historical_max_pain['values'][-3]
            acceleration = last_shift - previous_shift

        return {
            'daily_shift': daily_shift,
            'weekly_shift': weekly_shift,
            'trend': trend,
            'acceleration': acceleration
        }

    def _generate_max_pain_shift_analysis(self, current_max_pain, max_pain_shift, current_price, historical_max_pain):
        """Generate analysis of max pain shifts"""
        analysis = []

        # Current max pain
        distance_from_price = abs(current_max_pain - current_price)
        distance_pct = (distance_from_price / current_price) * 100

        analysis.append(f"Current Max Pain: {current_max_pain}, which is {distance_pct:.1f}% {'above' if current_max_pain > current_price else 'below'} the current price.")

        # Max pain shift analysis
        if max_pain_shift['daily_shift'] != 0:
            analysis.append(f"Max Pain shifted {abs(max_pain_shift['daily_shift'])} points {'upward' if max_pain_shift['daily_shift'] > 0 else 'downward'} in the last session.")

        if max_pain_shift['weekly_shift'] != 0:
            analysis.append(f"Over the past week, Max Pain has shifted {abs(max_pain_shift['weekly_shift'])} points {'upward' if max_pain_shift['weekly_shift'] > 0 else 'downward'}.")

        # Trend analysis
        if max_pain_shift['trend'] != 'Neutral':
            analysis.append(f"Max Pain shows a {max_pain_shift['trend']} trend.")

            if 'Upward' in max_pain_shift['trend']:
                analysis.append("Rising Max Pain typically indicates increasing call writing or put buying, suggesting bullish sentiment.")
            elif 'Downward' in max_pain_shift['trend']:
                analysis.append("Falling Max Pain typically indicates increasing put writing or call buying, suggesting bearish sentiment.")

        # Acceleration analysis
        if abs(max_pain_shift['acceleration']) > 0:
            analysis.append(f"The rate of Max Pain movement is {'accelerating' if max_pain_shift['acceleration'] > 0 else 'decelerating'}.")

            if max_pain_shift['acceleration'] > 0 and max_pain_shift['daily_shift'] > 0:
                analysis.append("Accelerating upward movement in Max Pain suggests strengthening bullish sentiment.")
            elif max_pain_shift['acceleration'] < 0 and max_pain_shift['daily_shift'] < 0:
                analysis.append("Accelerating downward movement in Max Pain suggests strengthening bearish sentiment.")

        # Price vs Max Pain analysis
        if len(historical_max_pain['dates']) > 5:
            # Check if price is moving toward or away from max pain
            recent_price_changes = [
                historical_max_pain['price'][i] - historical_max_pain['price'][i-1]
                for i in range(len(historical_max_pain['price'])-1, max(0, len(historical_max_pain['price'])-4), -1)
            ]

            recent_max_pain_changes = [
                historical_max_pain['values'][i] - historical_max_pain['values'][i-1]
                for i in range(len(historical_max_pain['values'])-1, max(0, len(historical_max_pain['values'])-4), -1)
            ]

            # Check if they're moving in the same direction
            if all(p > 0 for p in recent_price_changes) and all(m > 0 for m in recent_max_pain_changes):
                analysis.append("Price and Max Pain are both moving upward, reinforcing the bullish trend.")
            elif all(p < 0 for p in recent_price_changes) and all(m < 0 for m in recent_max_pain_changes):
                analysis.append("Price and Max Pain are both moving downward, reinforcing the bearish trend.")
            elif all(p > 0 for p in recent_price_changes) and all(m < 0 for m in recent_max_pain_changes):
                analysis.append("Price is moving up while Max Pain is moving down, suggesting potential resistance ahead.")
            elif all(p < 0 for p in recent_price_changes) and all(m > 0 for m in recent_max_pain_changes):
                analysis.append("Price is moving down while Max Pain is moving up, suggesting potential support ahead.")

        # Expiry implications
        if distance_pct < 2:
            analysis.append("Price is very close to Max Pain, suggesting it may gravitate toward this level as expiry approaches.")
        elif distance_pct < 5:
            analysis.append("Price is relatively close to Max Pain and may be drawn toward this level as expiry approaches.")
        else:
            analysis.append("Price is far from Max Pain, but may still be influenced by this level as expiry approaches.")

        return " ".join(analysis)
